import type { Config } from "tailwindcss";
import mcdesignTokens from './tokens/mcdesign-tokens.json';

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // MCDesign Token Colors
        fsm: {
          defining: mcdesignTokens.color.fsm.defining,
          onboarding: mcdesignTokens.color.fsm.onboarding,
          validating: mcdesignTokens.color.fsm.validating,
          'ready-promote': mcdesignTokens.color.fsm.ready_promote,
          promoting: mcdesignTokens.color.fsm.promoting,
          active: mcdesignTokens.color.fsm.active,
          'atom-triggered': mcdesignTokens.color.fsm.atom_triggered,
          'subnominal-drift': mcdesignTokens.color.fsm.subnominal_drift,
          degrading: mcdesignTokens.color.fsm.degrading,
          'quantum-ops': mcdesignTokens.color.fsm.quantum_ops,
          'govern-review': mcdesignTokens.color.fsm.govern_review,
          retiring: mcdesignTokens.color.fsm.retiring,
          quarantined: mcdesignTokens.color.fsm.quarantined,
          archived: mcdesignTokens.color.fsm.archived,
        },
        gal: {
          0: mcdesignTokens.color.gal.gal0,
          1: mcdesignTokens.color.gal.gal1,
          2: mcdesignTokens.color.gal.gal2,
          3: mcdesignTokens.color.gal.gal3,
          4: mcdesignTokens.color.gal.gal4,
          5: mcdesignTokens.color.gal.gal5,
        },
        'cognitive-load': {
          low: mcdesignTokens.color.cognitive.load.low,
          medium: mcdesignTokens.color.cognitive.load.medium,
          high: mcdesignTokens.color.cognitive.load.high,
          critical: mcdesignTokens.color.cognitive.load.critical,
        },
        ai: {
          idle: mcdesignTokens.color.ai.idle,
          thinking: mcdesignTokens.color.ai.thinking,
          generating: mcdesignTokens.color.ai.generating,
          streaming: mcdesignTokens.color.ai.streaming,
          error: mcdesignTokens.color.ai.error,
          awaiting: mcdesignTokens.color.ai.awaiting,
          completed: mcdesignTokens.color.ai.completed,
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      // Enhanced spacing for WCAG 3.0 compliance
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Enhanced font sizes for accessibility
      fontSize: {
        '2xs': ['0.625rem', { lineHeight: '0.75rem' }],
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
