'use client';

import { useState, useCallback } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FSMIndicator } from '@/components/ui/fsm-indicator';
import { GALIndicator } from '@/components/ui/gal-indicator';
import { CognitiveLoad } from '@/components/ui/cognitive-load';
import { AIIndicator } from '@/components/ui/ai-indicator';

/**
 * Performance Test Component
 * 
 * Tests the performance impact of MCStack components
 */
export function PerformanceTest() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<{
    componentRender: number;
    tokenAccess: number;
    massRender: number;
  } | null>(null);

  // Test component rendering performance
  const testComponentRender = useCallback(() => {
    const start = performance.now();
    
    // Simulate rendering multiple components
    for (let i = 0; i < 1000; i++) {
      // This simulates the work done during component render
      const element = document.createElement('div');
      element.className = 'bg-fsm-active text-white p-2';
      element.textContent = `Component ${i}`;
    }
    
    const end = performance.now();
    return end - start;
  }, []);

  // Test token access performance
  const testTokenAccess = useCallback(() => {
    const start = performance.now();
    
    // Test CSS variable access
    for (let i = 0; i < 1000; i++) {
      const style = getComputedStyle(document.documentElement);
      style.getPropertyValue('--color-fsm-active');
      style.getPropertyValue('--color-gal-gal3');
      style.getPropertyValue('--color-cognitive-load-high');
      style.getPropertyValue('--color-ai-thinking');
    }
    
    const end = performance.now();
    return end - start;
  }, []);

  // Test mass component rendering
  const testMassRender = useCallback(() => {
    const start = performance.now();
    
    // Create a temporary container
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    document.body.appendChild(container);
    
    // Render many components
    for (let i = 0; i < 100; i++) {
      const card = document.createElement('div');
      card.className = 'bg-card border rounded-lg p-4 space-y-2';
      
      const indicator = document.createElement('div');
      indicator.className = 'w-6 h-6 bg-fsm-active rounded-full';
      
      const gal = document.createElement('div');
      gal.className = 'w-6 h-6 bg-gal-3 rounded-full';
      
      const load = document.createElement('div');
      load.className = 'w-full h-2 bg-cognitive-load-medium rounded-full';
      
      card.appendChild(indicator);
      card.appendChild(gal);
      card.appendChild(load);
      container.appendChild(card);
    }
    
    const end = performance.now();
    
    // Cleanup
    document.body.removeChild(container);
    
    return end - start;
  }, []);

  const runPerformanceTests = useCallback(async () => {
    setIsRunning(true);
    
    // Wait a bit to ensure UI updates
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const componentRender = testComponentRender();
    await new Promise(resolve => setTimeout(resolve, 50));
    
    const tokenAccess = testTokenAccess();
    await new Promise(resolve => setTimeout(resolve, 50));
    
    const massRender = testMassRender();
    
    setResults({
      componentRender,
      tokenAccess,
      massRender
    });
    
    setIsRunning(false);
  }, [testComponentRender, testTokenAccess, testMassRender]);

  const getPerformanceRating = (time: number, type: string) => {
    let threshold = 10; // Default threshold
    
    switch (type) {
      case 'componentRender':
        threshold = 5; // 5ms for 1000 virtual renders
        break;
      case 'tokenAccess':
        threshold = 10; // 10ms for 4000 CSS variable accesses
        break;
      case 'massRender':
        threshold = 20; // 20ms for 100 DOM elements
        break;
    }
    
    if (time < threshold) return { rating: 'Excellent', color: 'text-green-600' };
    if (time < threshold * 2) return { rating: 'Good', color: 'text-yellow-600' };
    return { rating: 'Needs Optimization', color: 'text-red-600' };
  };

  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Performance Test
          <Button 
            onClick={runPerformanceTests} 
            disabled={isRunning}
            size="sm"
          >
            {isRunning ? 'Running...' : 'Run Tests'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isRunning && (
          <div className="flex items-center gap-2 mb-4">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span>Running performance tests...</span>
          </div>
        )}

        {results && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">Component Rendering</h3>
                <div className="text-2xl font-bold">{results.componentRender.toFixed(2)}ms</div>
                <div className={`text-sm ${getPerformanceRating(results.componentRender, 'componentRender').color}`}>
                  {getPerformanceRating(results.componentRender, 'componentRender').rating}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  1000 virtual component renders
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">Token Access</h3>
                <div className="text-2xl font-bold">{results.tokenAccess.toFixed(2)}ms</div>
                <div className={`text-sm ${getPerformanceRating(results.tokenAccess, 'tokenAccess').color}`}>
                  {getPerformanceRating(results.tokenAccess, 'tokenAccess').rating}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  4000 CSS variable accesses
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">Mass Rendering</h3>
                <div className="text-2xl font-bold">{results.massRender.toFixed(2)}ms</div>
                <div className={`text-sm ${getPerformanceRating(results.massRender, 'massRender').color}`}>
                  {getPerformanceRating(results.massRender, 'massRender').rating}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  100 DOM elements with MCStack classes
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Performance Summary</h3>
              <div className="text-sm space-y-1">
                <div>• Component rendering is optimized for high-frequency updates</div>
                <div>• Token access leverages browser-native CSS custom properties</div>
                <div>• Mass rendering tests real-world usage scenarios</div>
                <div>• All tests should complete in under 50ms for optimal UX</div>
              </div>
            </div>
          </div>
        )}

        {!results && !isRunning && (
          <div className="text-center py-8 text-muted-foreground">
            Click &quot;Run Tests&quot; to measure MCStack component performance
          </div>
        )}

        {/* Live Component Samples */}
        <div className="mt-6 pt-6 border-t">
          <h3 className="text-lg font-semibold mb-4">Live Component Samples</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-3 border rounded-lg text-center">
              <FSMIndicator state="active" size="lg" />
              <div className="text-xs mt-2">FSM Active</div>
            </div>
            <div className="p-3 border rounded-lg text-center">
              <GALIndicator level={3} size="lg" />
              <div className="text-xs mt-2">GAL Level 3</div>
            </div>
            <div className="p-3 border rounded-lg">
              <CognitiveLoad value={0.6} showValue />
              <div className="text-xs mt-2 text-center">Cognitive Load</div>
            </div>
            <div className="p-3 border rounded-lg text-center">
              <AIIndicator state="thinking" size="lg" animate />
              <div className="text-xs mt-2">AI Thinking</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
