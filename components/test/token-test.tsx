'use client';

import { useEffect, useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { getCSSVariable } from '@/components/providers/mcdesign-provider';
import { getToken } from '@/lib/design-tokens';

/**
 * Token Test Component
 * 
 * Verifies that MCDesign tokens are properly loaded and accessible
 */
export function TokenTest() {
  const [tokenTests, setTokenTests] = useState<Array<{
    name: string;
    expected: string;
    actual: string;
    status: 'pass' | 'fail' | 'loading';
  }>>([]);

  useEffect(() => {
    // Test key tokens
    const tests = [
      {
        name: 'FSM Active Color',
        expected: '#10b981',
        actual: '',
        status: 'loading' as const
      },
      {
        name: 'GAL Level 3 Color',
        expected: '#fbbf24',
        actual: '',
        status: 'loading' as const
      },
      {
        name: 'Cognitive Load High',
        expected: '#f97316',
        actual: '',
        status: 'loading' as const
      },
      {
        name: 'AI Thinking Color',
        expected: '#60a5fa',
        actual: '',
        status: 'loading' as const
      },
      {
        name: 'Brand Teal',
        expected: '#38a3a5',
        actual: '',
        status: 'loading' as const
      }
    ];

    // Test CSS variables
    setTimeout(() => {
      const updatedTests = tests.map(test => {
        let actual = '';
        let status: 'pass' | 'fail' = 'fail';

        try {
          switch (test.name) {
            case 'FSM Active Color':
              actual = getCSSVariable('--color-fsm-active') || getToken('color.fsm.active');
              break;
            case 'GAL Level 3 Color':
              actual = getCSSVariable('--color-gal-gal3') || getToken('color.gal.gal3');
              break;
            case 'Cognitive Load High':
              actual = getCSSVariable('--color-cognitive-load-high') || getToken('color.cognitive.load.high');
              break;
            case 'AI Thinking Color':
              actual = getCSSVariable('--color-ai-thinking') || getToken('color.ai.thinking');
              break;
            case 'Brand Teal':
              actual = getCSSVariable('--color-brand-teal') || getToken('color.brand.teal');
              break;
          }

          status = actual.toLowerCase() === test.expected.toLowerCase() ? 'pass' : 'fail';
        } catch (error) {
          console.error(`Error testing ${test.name}:`, error);
          actual = 'Error';
          status = 'fail';
        }

        return { ...test, actual, status };
      });

      setTokenTests(updatedTests);
    }, 100); // Small delay to ensure tokens are applied
  }, []);

  const passedTests = tokenTests.filter(test => test.status === 'pass').length;
  const totalTests = tokenTests.length;

  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Token System Test
          <span className={`text-sm px-2 py-1 rounded ${
            passedTests === totalTests ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {passedTests}/{totalTests} Passed
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {tokenTests.map((test, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded">
              <div className="flex items-center gap-3">
                <div className={`w-4 h-4 rounded-full ${
                  test.status === 'loading' ? 'bg-gray-300 animate-pulse' :
                  test.status === 'pass' ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <span className="font-medium">{test.name}</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <span>Expected:</span>
                  <div 
                    className="w-6 h-6 border rounded"
                    style={{ backgroundColor: test.expected }}
                    title={test.expected}
                  />
                  <code className="text-xs">{test.expected}</code>
                </div>
                <div className="flex items-center gap-2">
                  <span>Actual:</span>
                  <div 
                    className="w-6 h-6 border rounded"
                    style={{ backgroundColor: test.actual }}
                    title={test.actual}
                  />
                  <code className="text-xs">{test.actual || 'Loading...'}</code>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Visual Token Showcase */}
        <div className="mt-6 pt-6 border-t">
          <h3 className="text-lg font-semibold mb-4">Visual Token Showcase</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* FSM Colors */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">FSM States</h4>
              <div className="space-y-1">
                {['active', 'degrading', 'quarantined', 'archived'].map(state => (
                  <div key={state} className="flex items-center gap-2">
                    <div 
                      className={`w-4 h-4 rounded bg-fsm-${state}`}
                      title={`FSM ${state}`}
                    />
                    <span className="text-xs capitalize">{state}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* GAL Colors */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">GAL Levels</h4>
              <div className="space-y-1">
                {[0, 1, 2, 3, 4, 5].map(level => (
                  <div key={level} className="flex items-center gap-2">
                    <div 
                      className={`w-4 h-4 rounded bg-gal-${level}`}
                      title={`GAL ${level}`}
                    />
                    <span className="text-xs">GAL {level}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Cognitive Load Colors */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Cognitive Load</h4>
              <div className="space-y-1">
                {['low', 'medium', 'high', 'critical'].map(level => (
                  <div key={level} className="flex items-center gap-2">
                    <div 
                      className={`w-4 h-4 rounded bg-cognitive-load-${level}`}
                      title={`Cognitive Load ${level}`}
                    />
                    <span className="text-xs capitalize">{level}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* AI State Colors */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">AI States</h4>
              <div className="space-y-1">
                {['idle', 'thinking', 'generating', 'error'].map(state => (
                  <div key={state} className="flex items-center gap-2">
                    <div 
                      className={`w-4 h-4 rounded bg-ai-${state}`}
                      title={`AI ${state}`}
                    />
                    <span className="text-xs capitalize">{state}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
