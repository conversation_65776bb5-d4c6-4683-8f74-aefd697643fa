'use client';

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { FSMIndicator, FSMBadge, type FSMState } from '@/components/ui/fsm-indicator';
import { GALIndicator, GALBadge, GALProgress, type GALLevel } from '@/components/ui/gal-indicator';
import { CognitiveLoad, CognitiveLoadBadge, CognitiveLoadMeter } from '@/components/ui/cognitive-load';
import { AIIndicator, AIBadge, AIProgress, type AIState } from '@/components/ui/ai-indicator';
import { Button } from '@/components/ui/button';
import { MCStack } from '@/lib/mcstack';

/**
 * Interactive MCStack Demo Component
 * 
 * Demonstrates the full integration of MCDesign tokens with
 * interactive state management following MCStack principles.
 */
export function MCStackDemo() {
  const [fsmState, setFsmState] = useState<FSMState>('active');
  const [galLevel, setGalLevel] = useState<GALLevel>(3);
  const [cognitiveLoad, setCognitiveLoad] = useState(0.4);
  const [aiState, setAiState] = useState<AIState>('idle');
  const [aiProgress, setAiProgress] = useState(0);

  // Simulate AI processing
  const simulateAIProcess = () => {
    setAiState('thinking');
    setAiProgress(0);
    
    const interval = setInterval(() => {
      setAiProgress(prev => {
        if (prev >= 1) {
          clearInterval(interval);
          setAiState('completed');
          setTimeout(() => setAiState('idle'), 2000);
          return 1;
        }
        return prev + 0.1;
      });
    }, 200);
  };

  // FSM state transitions
  const transitionFSM = () => {
    const transitions = MCStack.fsm.transitions[fsmState];
    if (transitions && transitions.length > 0) {
      const nextState = transitions[Math.floor(Math.random() * transitions.length)];
      setFsmState(nextState);
    }
  };

  // GAL level adjustment
  const adjustGAL = (direction: 'up' | 'down') => {
    const progression = MCStack.gal.progression[galLevel];
    const validLevels = progression?.filter(level => 
      direction === 'up' ? level > galLevel : level < galLevel
    );
    
    if (validLevels && validLevels.length > 0) {
      setGalLevel(validLevels[0]);
    }
  };

  return (
    <div className="space-y-8">
      {/* Interactive Control Panel */}
      <Card className="p-6">
        <CardHeader>
          <CardTitle>MCStack Interactive Demo</CardTitle>
          <CardDescription>
            Control and observe MCStack components in real-time
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* State Controls */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">FSM State</label>
              <div className="flex items-center gap-2">
                <FSMIndicator state={fsmState} />
                <Button size="sm" onClick={transitionFSM}>
                  Transition
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">GAL Level</label>
              <div className="flex items-center gap-2">
                <GALIndicator level={galLevel} />
                <div className="flex gap-1">
                  <Button size="sm" onClick={() => adjustGAL('down')} disabled={galLevel === 0}>
                    -
                  </Button>
                  <Button size="sm" onClick={() => adjustGAL('up')} disabled={galLevel === 5}>
                    +
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Cognitive Load</label>
              <div className="space-y-2">
                <CognitiveLoad value={cognitiveLoad} showValue />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={cognitiveLoad}
                  onChange={(e) => setCognitiveLoad(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">AI State</label>
              <div className="flex items-center gap-2">
                <AIIndicator state={aiState} progress={aiProgress} />
                <Button size="sm" onClick={simulateAIProcess} disabled={aiState !== 'idle'}>
                  Process
                </Button>
              </div>
            </div>
          </div>

          {/* Live Component Preview */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Live Component Preview</h3>
            <Card
              fsmState={fsmState}
              galLevel={galLevel}
              cognitiveLoad={cognitiveLoad}
              aiState={aiState}
              className="p-4"
            >
              <CardHeader>
                <CardTitle>Dynamic MCStack Component</CardTitle>
                <CardDescription>
                  This component updates in real-time based on your controls
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <strong>FSM:</strong> {fsmState}
                  </div>
                  <div>
                    <strong>GAL:</strong> {galLevel}
                  </div>
                  <div>
                    <strong>Load:</strong> {Math.round(cognitiveLoad * 100)}%
                  </div>
                  <div>
                    <strong>AI:</strong> {aiState}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Component Showcase */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* FSM Showcase */}
        <Card className="p-6">
          <CardHeader>
            <CardTitle>FSM States</CardTitle>
            <CardDescription>Finite State Machine indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Current State</h4>
              <FSMIndicator state={fsmState} showLabel size="lg" />
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">All States</h4>
              <div className="flex flex-wrap gap-1">
                {(['defining', 'active', 'degrading', 'quarantined', 'archived'] as const).map(state => (
                  <FSMBadge 
                    key={state} 
                    state={state}
                    className={state === fsmState ? 'ring-2 ring-offset-2' : ''}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* GAL Showcase */}
        <Card className="p-6">
          <CardHeader>
            <CardTitle>GAL Levels</CardTitle>
            <CardDescription>Governed Autonomy Level indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Current Level</h4>
              <GALIndicator level={galLevel} showDescription size="lg" />
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Progress</h4>
              <GALProgress level={galLevel} showLabels />
            </div>
          </CardContent>
        </Card>

        {/* Cognitive Load Showcase */}
        <Card className="p-6">
          <CardHeader>
            <CardTitle>Cognitive Load</CardTitle>
            <CardDescription>Interface complexity indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Current Load</h4>
              <CognitiveLoad value={cognitiveLoad} showLabel showValue />
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Meter View</h4>
              <div className="flex justify-center">
                <CognitiveLoadMeter value={cognitiveLoad} size="lg" />
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Badge</h4>
              <CognitiveLoadBadge value={cognitiveLoad} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Processing Demo */}
      {aiState !== 'idle' && (
        <Card className="p-6">
          <CardHeader>
            <CardTitle>AI Processing Demo</CardTitle>
            <CardDescription>Real-time AI state monitoring</CardDescription>
          </CardHeader>
          <CardContent>
            <AIProgress 
              state={aiState}
              progress={aiProgress}
              estimatedTime={aiState === 'thinking' ? '2-3 seconds' : undefined}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
