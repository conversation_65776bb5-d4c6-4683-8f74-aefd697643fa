import { cn } from "@/lib/utils";
import { getGALColor } from "@/lib/design-tokens";

// GAL (Governed Autonomy Level) types
export type GALLevel = 0 | 1 | 2 | 3 | 4 | 5;

// GAL Level descriptions for accessibility and governance
export const GAL_DESCRIPTIONS: Record<GALLevel, string> = {
  0: "No autonomy - Full human control required",
  1: "Minimal autonomy - Human approval for all actions", 
  2: "Low autonomy - Human approval for significant actions",
  3: "Medium autonomy - Human oversight with intervention capability",
  4: "High autonomy - Human monitoring with exception handling",
  5: "Full autonomy - Minimal human intervention required"
};

// GAL Level governance requirements
export const GAL_GOVERNANCE: Record<GALLevel, string> = {
  0: "Direct human operation",
  1: "Human-in-the-loop for all decisions",
  2: "Human approval for critical operations", 
  3: "Human oversight with veto power",
  4: "Human monitoring with alert system",
  5: "Autonomous operation with audit trail"
};

interface GALIndicatorProps {
  /**
   * Current GAL level (0-5)
   */
  level: GALLevel;
  /**
   * Size variant of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the governance description
   */
  showDescription?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Custom tooltip content
   */
  tooltip?: string;
  /**
   * Whether to show governance requirements
   */
  showGovernance?: boolean;
}

const sizeClasses = {
  sm: "w-16 h-16 text-lg min-w-[4rem] min-h-[4rem]",
  md: "w-24 h-24 text-2xl min-w-[6rem] min-h-[6rem]",
  lg: "w-32 h-32 text-4xl min-w-[8rem] min-h-[8rem]"
};

const textSizeClasses = {
  sm: "text-xs",
  md: "text-sm", 
  lg: "text-base"
};

/**
 * GAL (Governed Autonomy Level) Indicator Component
 * 
 * Displays the current autonomy level of a component following
 * MCStack governance principles for safe AI operation.
 */
export function GALIndicator({ 
  level, 
  size = 'md',
  showDescription = false,
  className,
  tooltip,
  showGovernance = false
}: GALIndicatorProps) {
  const galColor = `bg-gal-${level}`;
  const description = tooltip || GAL_DESCRIPTIONS[level];
  const governance = GAL_GOVERNANCE[level];
  
  // Determine text color based on GAL level for contrast
  const textColor = level <= 2 ? "text-white" : "text-black";
  
  const indicator = (
    <div 
      className={cn(
        "rounded-full flex items-center justify-center font-black transition-all duration-500 border-3 border-white/30",
        sizeClasses[size],
        galColor,
        textColor,
        "shadow-2xl hover:shadow-3xl hover:scale-110 hover:border-white/50",
        "backdrop-blur-sm bg-opacity-90 relative overflow-hidden",
        "before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/10 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        // Special styling for different autonomy levels
        level === 0 && "border-gray-400 border-dashed",
        level === 5 && "glow-premium",
        className
      )}
      title={showGovernance ? `${description} - ${governance}` : description}
      aria-label={`GAL Level ${level}: ${description}`}
      role="status"
      data-gal-level={level}
    >
      <span className="font-bold">
        {level}
      </span>
    </div>
  );

  if (showDescription) {
    return (
      <div className="flex items-center gap-3">
        {indicator}
        <div className="flex flex-col">
          <span className={cn("font-medium", textSizeClasses[size])}>
            GAL {level}
          </span>
          <span className={cn("text-muted-foreground", textSizeClasses[size])}>
            {GAL_DESCRIPTIONS[level]}
          </span>
          {showGovernance && (
            <span className={cn("text-xs text-muted-foreground italic")}>
              {governance}
            </span>
          )}
        </div>
      </div>
    );
  }

  return indicator;
}

/**
 * GAL Level Progress Bar - Shows autonomy progression
 */
export function GALProgress({ 
  level, 
  maxLevel = 5,
  className,
  showLabels = false
}: {
  level: GALLevel;
  maxLevel?: number;
  className?: string;
  showLabels?: boolean;
}) {
  return (
    <div className={cn("w-full", className)}>
      {showLabels && (
        <div className="flex justify-between text-xs text-muted-foreground mb-1">
          <span>Manual</span>
          <span>Autonomous</span>
        </div>
      )}
      <div className="flex gap-1">
        {Array.from({ length: maxLevel + 1 }, (_, i) => (
          <div
            key={i}
            className={cn(
              "flex-1 h-2 rounded-sm transition-all duration-300",
              i <= level 
                ? `bg-gal-${i}` 
                : "bg-gray-200 dark:bg-gray-700"
            )}
            title={`GAL ${i}: ${GAL_DESCRIPTIONS[i as GALLevel]}`}
          />
        ))}
      </div>
      {showLabels && (
        <div className="flex justify-between text-xs mt-1">
          <span>GAL 0</span>
          <span className="font-medium">Current: GAL {level}</span>
          <span>GAL {maxLevel}</span>
        </div>
      )}
    </div>
  );
}

/**
 * GAL Badge - Compact representation
 */
export function GALBadge({
  level,
  className,
  variant = 'default'
}: {
  level: GALLevel;
  className?: string;
  variant?: 'default' | 'outline';
}) {
  const galColor = `bg-gal-${level}`;
  const textColor = level <= 2 ? "text-white" : "text-black";
  
  if (variant === 'outline') {
    return (
      <span
        className={cn(
          "inline-flex items-center px-6 py-3 rounded-lg text-lg font-bold border-2 min-h-[3.5rem] min-w-[6rem]",
          `border-gal-${level}`,
          `text-gal-${level}`,
          "bg-transparent",
          className
        )}
        title={GAL_DESCRIPTIONS[level]}
        data-gal-level={level}
      >
        GAL {level}
      </span>
    );
  }

  return (
    <span
      className={cn(
        "inline-flex items-center px-6 py-3 rounded-lg text-lg font-bold min-h-[3.5rem] min-w-[6rem]",
        galColor,
        textColor,
        className
      )}
      title={GAL_DESCRIPTIONS[level]}
      data-gal-level={level}
    >
      GAL {level}
    </span>
  );
}

/**
 * Hook to get GAL level information and utilities
 */
export function useGALLevel(level: GALLevel) {
  return {
    level,
    color: getGALColor(`gal${level}` as keyof typeof import('@/tokens/mcdesign-tokens.json').color.gal),
    description: GAL_DESCRIPTIONS[level],
    governance: GAL_GOVERNANCE[level],
    isFullyAutonomous: level === 5,
    isFullyManual: level === 0,
    requiresHumanApproval: level <= 2,
    requiresHumanOversight: level <= 3,
    allowsAutonomousOperation: level >= 4
  };
}
