import { cn } from "@/lib/utils";
import { getFSMColor } from "@/lib/design-tokens";
import mcdesignTokens from '@/tokens/mcdesign-tokens.json';

// FSM State types based on MCDesign tokens
export type FSMState = keyof typeof mcdesignTokens.color.fsm;

// FSM State descriptions for accessibility and tooltips
export const FSM_STATE_DESCRIPTIONS: Record<FSMState, string> = {
  defining: "Component is being defined and configured",
  onboarding: "Component is in onboarding phase",
  validating: "Component is undergoing validation",
  ready_promote: "Component is ready for promotion",
  promoting: "Component is being promoted",
  active: "Component is active and operational",
  atom_triggered: "Component has been triggered by an atom",
  subnominal_drift: "Component is experiencing subnominal drift",
  degrading: "Component performance is degrading",
  quantum_ops: "Component is in quantum operations mode",
  govern_review: "Component is under governance review",
  retiring: "Component is being retired",
  quarantined: "Component is quarantined due to issues",
  archived: "Component has been archived"
};

interface FSMIndicatorProps {
  /**
   * Current FSM state
   */
  state: FSMState;
  /**
   * Size variant of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the state label
   */
  showLabel?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Custom tooltip content (overrides default description)
   */
  tooltip?: string;
  /**
   * Whether the indicator should pulse for active states
   */
  animate?: boolean;
}

const sizeClasses = {
  sm: "w-12 h-12 text-base min-w-[3rem] min-h-[3rem]",
  md: "w-16 h-16 text-lg min-w-[4rem] min-h-[4rem]",
  lg: "w-20 h-20 text-xl min-w-[5rem] min-h-[5rem]"
};

const labelSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base"
};

/**
 * FSM State Indicator Component
 * 
 * Displays the current Finite State Machine state of a component
 * following MCStack principles for system observability.
 */
export function FSMIndicator({ 
  state, 
  size = 'md',
  showLabel = false,
  className,
  tooltip,
  animate = false
}: FSMIndicatorProps) {
  const stateColor = `bg-fsm-${state.replace('_', '-')}`;
  const description = tooltip || FSM_STATE_DESCRIPTIONS[state];
  
  // Determine if state should animate
  const shouldAnimate = animate && ['active', 'validating', 'promoting', 'atom_triggered'].includes(state);
  
  const indicator = (
    <div 
      className={cn(
        "rounded-full flex items-center justify-center font-medium transition-all duration-300",
        sizeClasses[size],
        stateColor,
        shouldAnimate && "animate-pulse",
        // Special styling for certain states
        state === 'quarantined' && "border-2 border-dashed border-current",
        state === 'archived' && "opacity-60",
        state === 'degrading' && "opacity-90",
        className
      )}
      title={description}
      aria-label={`FSM State: ${state} - ${description}`}
      role="status"
      data-fsm-state={state}
    >
      {/* State abbreviation for better visibility */}
      <span className="text-white font-bold uppercase tracking-tight">
        {getStateAbbreviation(state)}
      </span>
    </div>
  );

  if (showLabel) {
    return (
      <div className="flex items-center gap-2">
        {indicator}
        <span className={cn("font-medium capitalize", labelSizeClasses[size])}>
          {state.replace('_', ' ')}
        </span>
      </div>
    );
  }

  return indicator;
}

/**
 * Get abbreviated representation of FSM state
 */
function getStateAbbreviation(state: FSMState): string {
  const abbreviations: Record<FSMState, string> = {
    defining: "DF",
    onboarding: "OB", 
    validating: "VL",
    ready_promote: "RP",
    promoting: "PR",
    active: "AC",
    atom_triggered: "AT",
    subnominal_drift: "SD",
    degrading: "DG",
    quantum_ops: "QO",
    govern_review: "GR",
    retiring: "RT",
    quarantined: "QT",
    archived: "AR"
  };
  
  return abbreviations[state] || state.slice(0, 2).toUpperCase();
}

/**
 * FSM State Badge - Alternative presentation as a badge
 */
export function FSMBadge({
  state,
  className,
  ...props
}: Omit<FSMIndicatorProps, 'size' | 'showLabel'>) {
  const stateColor = `bg-fsm-${state.replace('_', '-')}`;
  
  return (
    <span
      className={cn(
        "inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold text-white min-h-[2.75rem] min-w-[2.75rem]",
        stateColor,
        state === 'quarantined' && "border border-dashed border-current",
        state === 'archived' && "opacity-60",
        className
      )}
      title={FSM_STATE_DESCRIPTIONS[state]}
      aria-label={`FSM State: ${state}`}
      data-fsm-state={state}
      {...props}
    >
      {state.replace('_', ' ')}
    </span>
  );
}

/**
 * Hook to get FSM state styling information
 */
export function useFSMState(state: FSMState) {
  return {
    color: getFSMColor(state),
    description: FSM_STATE_DESCRIPTIONS[state],
    abbreviation: getStateAbbreviation(state),
    isActive: ['active', 'validating', 'promoting'].includes(state),
    isWarning: ['degrading', 'subnominal_drift', 'retiring'].includes(state),
    isError: ['quarantined'].includes(state),
    isInactive: ['archived'].includes(state)
  };
}
