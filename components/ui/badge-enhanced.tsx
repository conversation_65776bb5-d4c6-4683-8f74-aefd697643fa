import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full border font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground border-border hover:bg-accent hover:text-accent-foreground",
        success: "border-transparent bg-green-500 text-white shadow hover:bg-green-600",
        warning: "border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",
        info: "border-transparent bg-blue-500 text-white shadow hover:bg-blue-600",
        // MCStack specific variants
        fsm: "border-transparent text-white shadow-sm",
        gal: "border-transparent text-white shadow-sm font-bold",
        cognitive: "border-transparent text-white shadow-sm",
        ai: "border-transparent text-white shadow-sm"
      },
      size: {
        sm: "h-8 px-3 text-sm min-w-[2rem] min-h-[2rem]", // WCAG 3.0 touch target
        default: "h-9 px-4 text-sm min-w-[2.25rem] min-h-[2.25rem]",
        lg: "h-11 px-6 text-base min-w-[2.75rem] min-h-[2.75rem]",
        xl: "h-12 px-8 text-lg min-w-[3rem] min-h-[3rem]"
      },
      spacing: {
        comfortable: "px-4 py-2 gap-2",
        standard: "px-3 py-1.5 gap-1.5", 
        dense: "px-2 py-1 gap-1"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      spacing: "standard"
    },
  }
);

export interface BadgeProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onCopy'>,
    VariantProps<typeof badgeVariants> {
  /**
   * Whether the badge is interactive (clickable)
   */
  interactive?: boolean;
  /**
   * Icon to display before the text
   */
  icon?: React.ReactNode;
  /**
   * Icon to display after the text
   */
  endIcon?: React.ReactNode;
  /**
   * Whether to show a close button
   */
  dismissible?: boolean;
  /**
   * Callback when close button is clicked
   */
  onDismiss?: () => void;
  /**
   * ARIA label for accessibility
   */
  ariaLabel?: string;
}

function Badge({
  className,
  variant,
  size,
  spacing,
  interactive = false,
  icon,
  endIcon,
  dismissible = false,
  onDismiss,
  ariaLabel,
  children,
  ...props
}: BadgeProps) {
  const baseClasses = cn(badgeVariants({ variant, size, spacing }), className);
  const baseProps = {
    "aria-label": ariaLabel,
    children: (
      <>
        {icon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {icon}
          </span>
        )}

        <span className="truncate leading-none tracking-wide">
          {children}
        </span>

        {endIcon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {endIcon}
          </span>
        )}

        {dismissible && (
          <button
            type="button"
            className="ml-2 flex-shrink-0 rounded-full p-1 hover:bg-black/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/50"
            onClick={onDismiss}
            aria-label="Remove badge"
          >
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </>
    )
  };

  if (interactive) {
    return (
      <button
        className={baseClasses}
        {...baseProps}
        {...(props as React.ButtonHTMLAttributes<HTMLButtonElement>)}
      />
    );
  }

  return (
    <div
      className={baseClasses}
      {...baseProps}
      {...(props as React.HTMLAttributes<HTMLDivElement>)}
    />
  );
}

/**
 * Enhanced Badge Group for managing multiple badges
 */
interface BadgeGroupProps {
  children: React.ReactNode;
  className?: string;
  /**
   * Maximum number of badges to show before truncating
   */
  maxVisible?: number;
  /**
   * Spacing between badges
   */
  gap?: "sm" | "md" | "lg";
  /**
   * Whether badges should wrap to new lines
   */
  wrap?: boolean;
}

function BadgeGroup({ 
  children, 
  className, 
  maxVisible,
  gap = "md",
  wrap = true 
}: BadgeGroupProps) {
  const badges = React.Children.toArray(children);
  const visibleBadges = maxVisible ? badges.slice(0, maxVisible) : badges;
  const hiddenCount = maxVisible ? Math.max(0, badges.length - maxVisible) : 0;
  
  const gapClasses = {
    sm: "gap-1",
    md: "gap-2", 
    lg: "gap-3"
  };
  
  return (
    <div 
      className={cn(
        "flex items-center",
        gapClasses[gap],
        wrap ? "flex-wrap" : "flex-nowrap",
        className
      )}
      role="group"
      aria-label="Badge group"
    >
      {visibleBadges}
      
      {hiddenCount > 0 && (
        <Badge 
          variant="outline" 
          size="sm"
          ariaLabel={`${hiddenCount} more items`}
        >
          +{hiddenCount}
        </Badge>
      )}
    </div>
  );
}

/**
 * Status Badge with predefined colors and meanings
 */
interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'success' | 'warning' | 'error' | 'info' | 'neutral';
  pulse?: boolean;
}

function StatusBadge({ 
  status, 
  pulse = false, 
  className, 
  children, 
  ...props 
}: StatusBadgeProps) {
  const statusVariants = {
    success: "bg-green-500 hover:bg-green-600",
    warning: "bg-yellow-500 hover:bg-yellow-600", 
    error: "bg-red-500 hover:bg-red-600",
    info: "bg-blue-500 hover:bg-blue-600",
    neutral: "bg-gray-500 hover:bg-gray-600"
  };
  
  return (
    <Badge
      className={cn(
        statusVariants[status],
        "text-white border-transparent",
        pulse && "animate-pulse",
        className
      )}
      ariaLabel={`Status: ${status}`}
      {...props}
    >
      {children}
    </Badge>
  );
}

/**
 * Interactive Tag component for filtering and selection
 */
interface TagProps extends Omit<BadgeProps, 'interactive'> {
  selected?: boolean;
  onToggle?: () => void;
  count?: number;
}

function Tag({ 
  selected = false, 
  onToggle, 
  count, 
  className, 
  children, 
  ...props 
}: TagProps) {
  return (
    <Badge
      interactive={!!onToggle}
      className={cn(
        "cursor-pointer transition-all duration-200",
        selected 
          ? "bg-primary text-primary-foreground shadow-md scale-105" 
          : "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        "focus-visible:ring-2 focus-visible:ring-primary/50",
        className
      )}
      onClick={onToggle}
      ariaLabel={`${selected ? 'Remove' : 'Add'} tag: ${children}`}
      role="button"
      aria-pressed={selected}
      {...props}
    >
      {children}
      {count !== undefined && (
        <span className="ml-1 text-xs opacity-75">
          ({count})
        </span>
      )}
    </Badge>
  );
}

export { Badge, BadgeGroup, StatusBadge, Tag, badgeVariants };
export type { BadgeGroupProps, StatusBadgeProps, TagProps };
