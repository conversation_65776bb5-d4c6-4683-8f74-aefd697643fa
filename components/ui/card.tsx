import * as React from "react"

import { cn } from "@/lib/utils"
import { FSMIndicator, type FSMState } from "./fsm-indicator"
import { GALIndicator, type GALLevel } from "./gal-indicator"
import { CognitiveLoad } from "./cognitive-load"
import { AIIndicator, type AIState } from "./ai-indicator"

interface MCStackCardProps extends Omit<React.ComponentProps<"div">, 'className'> {
  className?: string;
  /**
   * FSM state of the component
   */
  fsmState?: FSMState;
  /**
   * GAL (Governed Autonomy Level) of the component
   */
  galLevel?: GALLevel;
  /**
   * Cognitive load value (0-1)
   */
  cognitiveLoad?: number;
  /**
   * AI state if component involves AI operations
   */
  aiState?: AIState;
  /**
   * Whether to show MCStack indicators
   */
  showIndicators?: boolean;
  /**
   * Position of indicators
   */
  indicatorPosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

function Card({
  className,
  fsmState,
  galLevel,
  cognitiveLoad,
  aiState,
  showIndicators = true,
  indicatorPosition = 'top-right',
  ...props
}: MCStackCardProps) {
  const hasIndicators = showIndicators && (fsmState || galLevel !== undefined || cognitiveLoad !== undefined || aiState);

  const positionClasses = {
    'top-right': 'top-2 right-2',
    'top-left': 'top-2 left-2',
    'bottom-right': 'bottom-2 right-2',
    'bottom-left': 'bottom-2 left-2'
  };

  return (
    <div
      data-slot="card"
      className={cn(
        "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm relative",
        // FSM state styling
        fsmState === 'quarantined' && "border-dashed border-2 border-fsm-quarantined",
        fsmState === 'archived' && "opacity-60",
        fsmState === 'degrading' && "border-fsm-degrading",
        fsmState === 'active' && "border-fsm-active",
        // Cognitive load styling
        cognitiveLoad !== undefined && cognitiveLoad > 0.8 && "ring-2 ring-cognitive-load-high/20",
        className
      )}
      data-fsm-state={fsmState}
      data-gal-level={galLevel}
      data-cognitive-load={cognitiveLoad}
      data-ai-state={aiState}
      {...props}
    >
      {hasIndicators && (
        <div className={cn(
          "absolute z-10 flex gap-1",
          positionClasses[indicatorPosition]
        )}>
          {fsmState && <FSMIndicator state={fsmState} size="sm" />}
          {galLevel !== undefined && <GALIndicator level={galLevel} size="sm" />}
          {aiState && <AIIndicator state={aiState} size="sm" />}
        </div>
      )}

      {cognitiveLoad !== undefined && (
        <div className="absolute bottom-0 left-0 right-0 px-6 pb-2">
          <CognitiveLoad value={cognitiveLoad} size="sm" />
        </div>
      )}

      {props.children}
    </div>
  )
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className
      )}
      {...props}
    />
  )
}

function CardTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-title"
      className={cn("leading-none font-semibold", className)}
      {...props}
    />
  )
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className
      )}
      {...props}
    />
  )
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6", className)}
      {...props}
    />
  )
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6 [.border-t]:pt-6", className)}
      {...props}
    />
  )
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
}
