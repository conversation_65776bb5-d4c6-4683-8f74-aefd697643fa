import { cn } from "@/lib/utils";
import { getAIColor } from "@/lib/design-tokens";
import mcdesignTokens from '@/tokens/mcdesign-tokens.json';

// AI State types based on MCDesign tokens
export type AIState = keyof typeof mcdesignTokens.color.ai;

// AI State descriptions for accessibility and user understanding
export const AI_STATE_DESCRIPTIONS: Record<AIState, string> = {
  idle: "AI system is idle and ready",
  thinking: "AI is processing and analyzing",
  generating: "AI is generating content or responses",
  streaming: "AI is streaming real-time output",
  error: "AI encountered an error",
  awaiting: "AI is waiting for input or approval",
  completed: "AI has completed the task successfully"
};

// AI State icons/symbols for visual representation
export const AI_STATE_SYMBOLS: Record<AIState, string> = {
  idle: "○",
  thinking: "◐",
  generating: "◑",
  streaming: "▶",
  error: "✕",
  awaiting: "⏸",
  completed: "✓"
};

interface AIIndicatorProps {
  /**
   * Current AI state
   */
  state: AIState;
  /**
   * Size variant of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the state label
   */
  showLabel?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Custom tooltip content
   */
  tooltip?: string;
  /**
   * Whether to animate active states
   */
  animate?: boolean;
  /**
   * Progress value for streaming/generating states (0-1)
   */
  progress?: number;
}

const sizeClasses = {
  sm: "w-4 h-4 text-xs",
  md: "w-6 h-6 text-sm",
  lg: "w-8 h-8 text-base"
};

const labelSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base"
};

/**
 * AI State Indicator Component
 * 
 * Displays the current state of AI operations following
 * MCStack principles for transparent AI system monitoring.
 */
export function AIIndicator({ 
  state, 
  size = 'md',
  showLabel = false,
  className,
  tooltip,
  animate = true,
  progress
}: AIIndicatorProps) {
  const aiColor = `bg-ai-${state}`;
  const description = tooltip || AI_STATE_DESCRIPTIONS[state];
  const symbol = AI_STATE_SYMBOLS[state];
  
  // Determine if state should animate
  const shouldAnimate = animate && ['thinking', 'generating', 'streaming'].includes(state);
  const shouldPulse = animate && ['awaiting', 'thinking'].includes(state);
  
  const indicator = (
    <div 
      className={cn(
        "rounded-full flex items-center justify-center font-bold transition-all duration-300 relative overflow-hidden",
        sizeClasses[size],
        aiColor,
        "text-white",
        shouldAnimate && "animate-spin",
        shouldPulse && "animate-pulse",
        state === 'error' && "animate-bounce",
        className
      )}
      title={description}
      aria-label={`AI State: ${state} - ${description}`}
      role="status"
      data-ai-state={state}
    >
      {/* Progress indicator for streaming/generating */}
      {progress !== undefined && ['streaming', 'generating'].includes(state) && (
        <div 
          className="absolute inset-0 bg-white/20 transition-all duration-300"
          style={{ 
            clipPath: `polygon(0 0, ${progress * 100}% 0, ${progress * 100}% 100%, 0 100%)` 
          }}
        />
      )}
      
      <span className="relative z-10">
        {symbol}
      </span>
    </div>
  );

  if (showLabel) {
    return (
      <div className="flex items-center gap-2">
        {indicator}
        <div className="flex flex-col">
          <span className={cn("font-medium capitalize", labelSizeClasses[size])}>
            {state}
          </span>
          {progress !== undefined && (
            <span className={cn("text-muted-foreground", labelSizeClasses[size])}>
              {Math.round(progress * 100)}%
            </span>
          )}
        </div>
      </div>
    );
  }

  return indicator;
}

/**
 * AI State Badge - Alternative presentation as a badge
 */
export function AIBadge({
  state,
  className,
  showProgress = false,
  progress,
  ...props
}: Omit<AIIndicatorProps, 'size' | 'showLabel'> & { showProgress?: boolean }) {
  const aiColor = `bg-ai-${state}`;
  
  return (
    <span
      className={cn(
        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white gap-1",
        aiColor,
        className
      )}
      title={AI_STATE_DESCRIPTIONS[state]}
      aria-label={`AI State: ${state}`}
      data-ai-state={state}
      {...props}
    >
      <span>{AI_STATE_SYMBOLS[state]}</span>
      <span className="capitalize">{state}</span>
      {showProgress && progress !== undefined && (
        <span className="text-white/80">
          {Math.round(progress * 100)}%
        </span>
      )}
    </span>
  );
}

/**
 * AI Progress Indicator - For streaming and generation tasks
 */
export function AIProgress({
  state,
  progress = 0,
  className,
  showPercentage = true,
  estimatedTime
}: {
  state: AIState;
  progress?: number;
  className?: string;
  showPercentage?: boolean;
  estimatedTime?: string;
}) {
  const clampedProgress = Math.max(0, Math.min(1, progress));
  const percentage = clampedProgress * 100;
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <AIBadge state={state} animate={true} />
        {showPercentage && (
          <span className="text-sm text-muted-foreground">
            {percentage.toFixed(0)}%
          </span>
        )}
      </div>
      
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <div 
          className={cn(
            "h-full transition-all duration-300 rounded-full",
            `bg-ai-${state}`,
            state === 'streaming' && "animate-pulse"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {estimatedTime && (
        <div className="text-xs text-muted-foreground text-center">
          Estimated time: {estimatedTime}
        </div>
      )}
    </div>
  );
}

/**
 * AI Status Panel - Comprehensive AI state display
 */
export function AIStatusPanel({
  state,
  progress,
  message,
  timestamp,
  className
}: {
  state: AIState;
  progress?: number;
  message?: string;
  timestamp?: Date;
  className?: string;
}) {
  return (
    <div className={cn(
      "p-4 rounded-lg border bg-card text-card-foreground",
      className
    )}>
      <div className="flex items-start justify-between mb-2">
        <AIIndicator state={state} size="lg" showLabel={true} progress={progress} />
        {timestamp && (
          <span className="text-xs text-muted-foreground">
            {timestamp.toLocaleTimeString()}
          </span>
        )}
      </div>
      
      {progress !== undefined && ['streaming', 'generating'].includes(state) && (
        <AIProgress 
          state={state} 
          progress={progress} 
          className="mb-2"
        />
      )}
      
      {message && (
        <p className="text-sm text-muted-foreground">
          {message}
        </p>
      )}
    </div>
  );
}

/**
 * Hook to get AI state information and utilities
 */
export function useAIState(state: AIState) {
  return {
    state,
    color: getAIColor(state),
    description: AI_STATE_DESCRIPTIONS[state],
    symbol: AI_STATE_SYMBOLS[state],
    isActive: ['thinking', 'generating', 'streaming'].includes(state),
    isWaiting: ['idle', 'awaiting'].includes(state),
    isError: state === 'error',
    isComplete: state === 'completed',
    shouldAnimate: ['thinking', 'generating', 'streaming'].includes(state)
  };
}
