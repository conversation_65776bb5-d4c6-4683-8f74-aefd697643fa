import { cn } from "@/lib/utils";
import { getAIColor } from "@/lib/design-tokens";
import mcdesignTokens from '@/tokens/mcdesign-tokens.json';

// AI State types based on MCDesign tokens
export type AIState = keyof typeof mcdesignTokens.color.ai;

// AI State descriptions for accessibility and user understanding
export const AI_STATE_DESCRIPTIONS: Record<AIState, string> = {
  idle: "AI system is idle and ready",
  thinking: "AI is processing and analyzing",
  generating: "AI is generating content or responses",
  streaming: "AI is streaming real-time output",
  error: "AI encountered an error",
  awaiting: "AI is waiting for input or approval",
  completed: "AI has completed the task successfully"
};

// AI State icons/symbols for visual representation
export const AI_STATE_SYMBOLS: Record<AIState, string> = {
  idle: "○",
  thinking: "◐",
  generating: "◑",
  streaming: "▶",
  error: "✕",
  awaiting: "⏸",
  completed: "✓"
};

interface AIIndicatorProps {
  /**
   * Current AI state
   */
  state: AIState;
  /**
   * Size variant of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the state label
   */
  showLabel?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Custom tooltip content
   */
  tooltip?: string;
  /**
   * Whether to animate active states
   */
  animate?: boolean;
  /**
   * Progress value for streaming/generating states (0-1)
   */
  progress?: number;
}

const sizeClasses = {
  sm: "w-11 h-11 text-sm min-w-[2.75rem] min-h-[2.75rem]", // WCAG 3.0 touch target
  md: "w-12 h-12 text-base min-w-[3rem] min-h-[3rem]",
  lg: "w-14 h-14 text-lg min-w-[3.5rem] min-h-[3.5rem]"
};

const labelSizeClasses = {
  sm: "text-sm leading-relaxed",
  md: "text-base leading-relaxed",
  lg: "text-lg leading-relaxed"
};

/**
 * AI State Indicator Component
 * 
 * Displays the current state of AI operations following
 * MCStack principles for transparent AI system monitoring.
 */
export function AIIndicator({ 
  state, 
  size = 'md',
  showLabel = false,
  className,
  tooltip,
  animate = true,
  progress
}: AIIndicatorProps) {
  const aiColor = `bg-ai-${state}`;
  const description = tooltip || AI_STATE_DESCRIPTIONS[state];
  const symbol = AI_STATE_SYMBOLS[state];
  
  const indicator = (
    <output
      className={cn(
        "rounded-full flex items-center justify-center font-bold transition-all duration-300 relative overflow-hidden border-2 border-transparent",
        sizeClasses[size],
        aiColor,
        "text-white shadow-lg",
        // Enhanced thinking animation with reduced motion support
        animate && state === 'thinking' && "shadow-ai-thinking",
        animate && state === 'generating' && "shadow-ai-generating",
        animate && state === 'streaming' && "shadow-ai-streaming",
        state === 'error' && "border-red-300",
        state === 'completed' && "shadow-ai-completed",
        className
      )}
      title={description}
      aria-label={`AI State: ${state} - ${description}`}
      aria-live="polite"
      aria-atomic="true"
      data-ai-state={state}
    >
      {/* Enhanced thinking indicator */}
      {state === 'thinking' && (
        <>
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 animate-pulse" />
          <div className="absolute inset-2 rounded-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 animate-ping" />
        </>
      )}

      {/* Progress indicator for streaming/generating */}
      {progress !== undefined && ['streaming', 'generating'].includes(state) && (
        <div
          className="absolute inset-0 bg-white/30 transition-all duration-500 rounded-full"
          style={{
            clipPath: `polygon(0 0, ${progress * 100}% 0, ${progress * 100}% 100%, 0 100%)`
          }}
          aria-hidden="true"
        />
      )}

      <span className="relative z-10 font-extrabold text-lg" aria-hidden="true">
        {symbol}
      </span>

      {/* Screen reader only progress announcement */}
      {progress !== undefined && (
        <span className="sr-only">
          {state} {Math.round(progress * 100)}% complete
        </span>
      )}
    </output>
  );

  if (showLabel) {
    return (
      <div className="flex items-center gap-2">
        {indicator}
        <div className="flex flex-col">
          <span className={cn("font-medium capitalize", labelSizeClasses[size])}>
            {state}
          </span>
          {progress !== undefined && (
            <span className={cn("text-muted-foreground", labelSizeClasses[size])}>
              {Math.round(progress * 100)}%
            </span>
          )}
        </div>
      </div>
    );
  }

  return indicator;
}

/**
 * AI State Badge - Alternative presentation as a badge
 */
export function AIBadge({
  state,
  className,
  showProgress = false,
  progress,
  ...props
}: Omit<AIIndicatorProps, 'size' | 'showLabel'> & { showProgress?: boolean }) {
  const aiColor = `bg-ai-${state}`;
  
  return (
    <span
      className={cn(
        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white gap-1",
        aiColor,
        className
      )}
      title={AI_STATE_DESCRIPTIONS[state]}
      aria-label={`AI State: ${state}`}
      data-ai-state={state}
      {...props}
    >
      <span>{AI_STATE_SYMBOLS[state]}</span>
      <span className="capitalize">{state}</span>
      {showProgress && progress !== undefined && (
        <span className="text-white/80">
          {Math.round(progress * 100)}%
        </span>
      )}
    </span>
  );
}

/**
 * AI Progress Indicator - For streaming and generation tasks
 */
export function AIProgress({
  state,
  progress = 0,
  className,
  showPercentage = true,
  estimatedTime
}: {
  state: AIState;
  progress?: number;
  className?: string;
  showPercentage?: boolean;
  estimatedTime?: string;
}) {
  const clampedProgress = Math.max(0, Math.min(1, progress));
  const percentage = clampedProgress * 100;
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <AIBadge state={state} animate={true} />
        {showPercentage && (
          <span className="text-sm text-muted-foreground">
            {percentage.toFixed(0)}%
          </span>
        )}
      </div>
      
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <div 
          className={cn(
            "h-full transition-all duration-300 rounded-full",
            `bg-ai-${state}`,
            state === 'streaming' && "animate-pulse"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {estimatedTime && (
        <div className="text-xs text-muted-foreground text-center">
          Estimated time: {estimatedTime}
        </div>
      )}
    </div>
  );
}

/**
 * AI Status Panel - Comprehensive AI state display
 */
export function AIStatusPanel({
  state,
  progress,
  message,
  timestamp,
  className
}: {
  state: AIState;
  progress?: number;
  message?: string;
  timestamp?: Date;
  className?: string;
}) {
  return (
    <div className={cn(
      "p-4 rounded-lg border bg-card text-card-foreground",
      className
    )}>
      <div className="flex items-start justify-between mb-2">
        <AIIndicator state={state} size="lg" showLabel={true} progress={progress} />
        {timestamp && (
          <span className="text-xs text-muted-foreground">
            {timestamp.toLocaleTimeString()}
          </span>
        )}
      </div>
      
      {progress !== undefined && ['streaming', 'generating'].includes(state) && (
        <AIProgress 
          state={state} 
          progress={progress} 
          className="mb-2"
        />
      )}
      
      {message && (
        <p className="text-sm text-muted-foreground">
          {message}
        </p>
      )}
    </div>
  );
}

/**
 * Hook to get AI state information and utilities
 */
export function useAIState(state: AIState) {
  return {
    state,
    color: getAIColor(state),
    description: AI_STATE_DESCRIPTIONS[state],
    symbol: AI_STATE_SYMBOLS[state],
    isActive: ['thinking', 'generating', 'streaming'].includes(state),
    isWaiting: ['idle', 'awaiting'].includes(state),
    isError: state === 'error',
    isComplete: state === 'completed',
    shouldAnimate: ['thinking', 'generating', 'streaming'].includes(state)
  };
}
