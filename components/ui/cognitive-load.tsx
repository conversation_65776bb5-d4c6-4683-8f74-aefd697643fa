import { cn } from "@/lib/utils";
import { getCognitiveLoadColor } from "@/lib/design-tokens";
import mcdesignTokens from '@/tokens/mcdesign-tokens.json';

// Cognitive load thresholds from tokens
const THRESHOLDS = mcdesignTokens.cognitive.load.threshold;

export type CognitiveLoadLevel = 'low' | 'medium' | 'high' | 'critical';

interface CognitiveLoadProps {
  /**
   * Cognitive load value (0-1 range)
   */
  value: number;
  /**
   * Size variant of the indicator
   */
  size?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the numeric value
   */
  showValue?: boolean;
  /**
   * Whether to show the load level label
   */
  showLabel?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Custom label for the indicator
   */
  label?: string;
  /**
   * Whether to animate the progress bar
   */
  animated?: boolean;
}

const sizeClasses = {
  sm: "h-1.5",
  md: "h-2",
  lg: "h-3"
};

const textSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base"
};

/**
 * Get cognitive load level based on value and thresholds
 */
function getCognitiveLoadLevel(value: number): CognitiveLoadLevel {
  if (value >= Number(THRESHOLDS.critical)) return 'critical';
  if (value >= Number(THRESHOLDS.high)) return 'high';
  if (value >= Number(THRESHOLDS.medium)) return 'medium';
  return 'low';
}

/**
 * Get cognitive load description
 */
function getCognitiveLoadDescription(level: CognitiveLoadLevel): string {
  const descriptions = {
    low: "Minimal cognitive effort required",
    medium: "Moderate cognitive effort required", 
    high: "High cognitive effort required",
    critical: "Critical cognitive load - consider simplification"
  };
  return descriptions[level];
}

/**
 * Cognitive Load Indicator Component
 * 
 * Displays the cognitive load of an interface element following
 * MCStack principles for human-centered design and accessibility.
 */
export function CognitiveLoad({ 
  value, 
  size = 'md',
  showValue = false,
  showLabel = false,
  className,
  label,
  animated = true
}: CognitiveLoadProps) {
  // Clamp value between 0 and 1
  const clampedValue = Math.max(0, Math.min(1, value));
  const percentage = clampedValue * 100;
  const level = getCognitiveLoadLevel(clampedValue);
  const description = getCognitiveLoadDescription(level);
  
  const progressBar = (
    <div 
      className={cn(
        "w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",
        sizeClasses[size],
        className
      )}
      role="progressbar"
      aria-valuenow={percentage}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-label={`Cognitive load: ${percentage.toFixed(0)}% - ${description}`}
      title={description}
    >
      <div 
        className={cn(
          "h-full transition-all duration-500 ease-out rounded-full",
          `bg-cognitive-load-${level}`,
          animated && "transition-all duration-700 ease-out"
        )}
        style={{ width: `${percentage}%` }}
        data-cognitive-load={clampedValue}
        data-cognitive-level={level}
      />
    </div>
  );

  if (showValue || showLabel) {
    return (
      <div className="space-y-1">
        {(showLabel || label) && (
          <div className="flex justify-between items-center">
            <span className={cn("font-medium", textSizeClasses[size])}>
              {label || "Cognitive Load"}
            </span>
            {showValue && (
              <span className={cn("text-muted-foreground", textSizeClasses[size])}>
                {percentage.toFixed(0)}%
              </span>
            )}
          </div>
        )}
        {progressBar}
        {showLabel && (
          <div className="flex justify-between items-center">
            <span className={cn("text-muted-foreground capitalize", textSizeClasses[size])}>
              {level} load
            </span>
            <span className={cn("text-xs text-muted-foreground")}>
              {description}
            </span>
          </div>
        )}
      </div>
    );
  }

  return progressBar;
}

/**
 * Cognitive Load Badge - Compact representation
 */
export function CognitiveLoadBadge({
  value,
  className,
  showPercentage = true
}: {
  value: number;
  className?: string;
  showPercentage?: boolean;
}) {
  const clampedValue = Math.max(0, Math.min(1, value));
  const percentage = clampedValue * 100;
  const level = getCognitiveLoadLevel(clampedValue);
  
  return (
    <span
      className={cn(
        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white",
        `bg-cognitive-load-${level}`,
        className
      )}
      title={getCognitiveLoadDescription(level)}
      data-cognitive-load={clampedValue}
    >
      {showPercentage ? `${percentage.toFixed(0)}%` : level}
    </span>
  );
}

/**
 * Cognitive Load Meter - Circular representation
 */
export function CognitiveLoadMeter({
  value,
  size = 'md',
  className,
  showValue = true
}: {
  value: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showValue?: boolean;
}) {
  const clampedValue = Math.max(0, Math.min(1, value));
  const percentage = clampedValue * 100;
  
  const sizeMap = {
    sm: { size: 40, stroke: 3 },
    md: { size: 60, stroke: 4 },
    lg: { size: 80, stroke: 5 }
  };
  
  const { size: circleSize, stroke } = sizeMap[size];
  const radius = (circleSize - stroke) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  
  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={circleSize}
        height={circleSize}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={stroke}
          fill="none"
          className="text-gray-200 dark:text-gray-700"
        />
        {/* Progress circle */}
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          stroke={getCognitiveLoadColor(clampedValue)}
          strokeWidth={stroke}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-700 ease-out"
        />
      </svg>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={cn("font-bold", textSizeClasses[size])}>
            {percentage.toFixed(0)}%
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * Hook to get cognitive load information and utilities
 */
export function useCognitiveLoad(value: number) {
  const clampedValue = Math.max(0, Math.min(1, value));
  const level = getCognitiveLoadLevel(clampedValue);
  
  return {
    value: clampedValue,
    percentage: clampedValue * 100,
    level,
    color: getCognitiveLoadColor(clampedValue),
    description: getCognitiveLoadDescription(level),
    isLow: level === 'low',
    isMedium: level === 'medium', 
    isHigh: level === 'high',
    isCritical: level === 'critical',
    needsSimplification: level === 'critical',
    thresholds: THRESHOLDS
  };
}
