'use client';

import { generateCssVariables } from '@/lib/design-tokens';
import { useEffect, useRef } from 'react';

interface MCDesignProviderProps {
  children: React.ReactNode;
  /**
   * Whether to apply tokens immediately on mount
   * @default true
   */
  applyTokens?: boolean;
  /**
   * Custom CSS variables to merge with MCDesign tokens
   */
  customVariables?: Record<string, string>;
}

/**
 * MCDesign Provider Component
 * 
 * Applies MCDesign tokens as CSS custom properties to the document root.
 * Follows MCStack principles for safe, governable token management.
 */
export function MCDesignProvider({ 
  children, 
  applyTokens = true,
  customVariables = {}
}: MCDesignProviderProps) {
  const appliedRef = useRef(false);

  useEffect(() => {
    if (!applyTokens || appliedRef.current) return;

    try {
      // Generate CSS variables from MCDesign tokens
      const cssVars = generateCssVariables();
      
      // Merge with custom variables
      const allVariables = { ...cssVars, ...customVariables };
      
      // Apply to document root
      const root = document.documentElement;
      
      Object.entries(allVariables).forEach(([key, value]) => {
        if (typeof value === 'string' && value.trim()) {
          root.style.setProperty(key, value);
        }
      });

      appliedRef.current = true;
      
      // Log successful application in development
      if (process.env.NODE_ENV === 'development') {
        console.log('MCDesign tokens applied:', Object.keys(allVariables).length, 'variables');
      }
    } catch (error) {
      console.error('Failed to apply MCDesign tokens:', error);
    }
  }, [applyTokens, customVariables]);

  return <>{children}</>;
}

/**
 * Hook to check if MCDesign tokens are available
 * @returns boolean indicating if tokens are applied
 */
export function useMCDesignTokens(): boolean {
  useEffect(() => {
    // Check if a known MCDesign token is available
    const testToken = getComputedStyle(document.documentElement)
      .getPropertyValue('--color-brand-teal');
    
    return Boolean(testToken);
  }, []);

  return true; // Assume tokens are available after provider mounts
}

/**
 * Utility to get CSS variable value at runtime
 * @param variableName - CSS variable name (with or without --)
 * @returns CSS variable value or empty string
 */
export function getCSSVariable(variableName: string): string {
  if (typeof window === 'undefined') return '';
  
  const name = variableName.startsWith('--') ? variableName : `--${variableName}`;
  return getComputedStyle(document.documentElement).getPropertyValue(name).trim();
}

/**
 * Utility to set CSS variable at runtime
 * @param variableName - CSS variable name (with or without --)
 * @param value - CSS value to set
 */
export function setCSSVariable(variableName: string, value: string): void {
  if (typeof window === 'undefined') return;
  
  const name = variableName.startsWith('--') ? variableName : `--${variableName}`;
  document.documentElement.style.setProperty(name, value);
}
