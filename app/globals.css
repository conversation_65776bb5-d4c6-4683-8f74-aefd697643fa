@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  /* MCDesign Token Integration */
  /* FSM State Colors */
  --color-fsm-defining: var(--color-fsm-defining);
  --color-fsm-onboarding: var(--color-fsm-onboarding);
  --color-fsm-validating: var(--color-fsm-validating);
  --color-fsm-ready-promote: var(--color-fsm-ready_promote);
  --color-fsm-promoting: var(--color-fsm-promoting);
  --color-fsm-active: var(--color-fsm-active);
  --color-fsm-atom-triggered: var(--color-fsm-atom_triggered);
  --color-fsm-subnominal-drift: var(--color-fsm-subnominal_drift);
  --color-fsm-degrading: var(--color-fsm-degrading);
  --color-fsm-quantum-ops: var(--color-fsm-quantum_ops);
  --color-fsm-govern-review: var(--color-fsm-govern_review);
  --color-fsm-retiring: var(--color-fsm-retiring);
  --color-fsm-quarantined: var(--color-fsm-quarantined);
  --color-fsm-archived: var(--color-fsm-archived);

  /* GAL Level Colors */
  --color-gal-0: var(--color-gal-gal0);
  --color-gal-1: var(--color-gal-gal1);
  --color-gal-2: var(--color-gal-gal2);
  --color-gal-3: var(--color-gal-gal3);
  --color-gal-4: var(--color-gal-gal4);
  --color-gal-5: var(--color-gal-gal5);

  /* Cognitive Load Colors */
  --color-cognitive-load-low: var(--color-cognitive-load-low);
  --color-cognitive-load-medium: var(--color-cognitive-load-medium);
  --color-cognitive-load-high: var(--color-cognitive-load-high);
  --color-cognitive-load-critical: var(--color-cognitive-load-critical);

  /* AI State Colors */
  --color-ai-idle: var(--color-ai-idle);
  --color-ai-thinking: var(--color-ai-thinking);
  --color-ai-generating: var(--color-ai-generating);
  --color-ai-streaming: var(--color-ai-streaming);
  --color-ai-error: var(--color-ai-error);
  --color-ai-awaiting: var(--color-ai-awaiting);
  --color-ai-completed: var(--color-ai-completed);

  /* MCDesign Brand Colors */
  --color-mcdesign-primary: var(--color-semantic-primary);
  --color-mcdesign-secondary: var(--color-semantic-secondary);
  --color-mcdesign-accent: var(--color-semantic-accent);

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

:root {
  --radius: 0.625rem;

  /* Light mode colors with WCAG 3.0 compliance */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #0f172a;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --chart-1: #3b82f6;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #8b5cf6;
  --sidebar: #ffffff;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #0f172a;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #0f172a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #3b82f6;

  /* WCAG 3.0 specific variables */
  --focus-ring: 3px solid #3b82f6;
  --focus-offset: 2px;
  --touch-target-min: 44px;
  --text-spacing: 0.16em;
}

.dark {
  /* Dark mode colors with WCAG 3.0 compliance */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #f8fafc;
  --primary-foreground: #0f172a;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #334155;
  --accent-foreground: #f8fafc;
  --destructive: #f87171;
  --destructive-foreground: #0f172a;
  --border: #334155;
  --input: #334155;
  --ring: #60a5fa;
  --chart-1: #60a5fa;
  --chart-2: #4ade80;
  --chart-3: #fbbf24;
  --chart-4: #f87171;
  --chart-5: #a855f7;
  --sidebar: #1e293b;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #60a5fa;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #60a5fa;

  /* Dark mode specific WCAG adjustments */
  --focus-ring: 3px solid #60a5fa;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-size: 1.125rem; /* 18px base for WCAG 3.0 compliance */
    line-height: 1.7; /* Enhanced line height for readability */
    letter-spacing: 0.02em;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 400;
  }

  /* WCAG 3.0 Focus Management */
  *:focus-visible {
    outline: var(--focus-ring);
    outline-offset: var(--focus-offset);
    border-radius: 0.25rem;
  }

  /* Remove default focus for mouse users */
  *:focus:not(:focus-visible) {
    outline: none;
  }

  /* Enhanced touch targets */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
  }

  /* Text spacing for readability */
  p, li, td, th {
    letter-spacing: var(--text-spacing);
    word-spacing: 0.16em;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --border: #000000;
      --ring: #000000;
    }

    .dark {
      --border: #ffffff;
      --ring: #ffffff;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Enhanced typography with WCAG 3.0 compliance */
  h1 {
    font-size: 2.5rem; /* 40px */
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.02em;
    color: var(--foreground);
  }

  h2 {
    font-size: 2rem; /* 32px */
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.015em;
    color: var(--foreground);
  }

  h3 {
    font-size: 1.5rem; /* 24px */
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: -0.01em;
    color: var(--foreground);
  }

  h4 {
    font-size: 1.25rem; /* 20px */
    font-weight: 600;
    line-height: 1.5;
    color: var(--foreground);
  }

  h5, h6 {
    font-size: 1.125rem; /* 18px */
    font-weight: 500;
    line-height: 1.6;
    color: var(--foreground);
  }

  /* Enhanced text elements */
  p {
    font-size: 1.125rem; /* 18px */
    line-height: 1.7;
    letter-spacing: 0.01em;
    margin-bottom: 1rem;
  }

  small {
    font-size: 1rem; /* 16px minimum for accessibility */
    line-height: 1.6;
  }

  /* Ensure sufficient color contrast for links */
  a {
    color: var(--primary);
    text-decoration-thickness: 2px;
    text-underline-offset: 0.125em;
  }

  a:hover, a:focus {
    text-decoration-thickness: 3px;
  }

  /* Enhanced button accessibility with WCAG 3.0 compliance */
  button, [role="button"] {
    min-height: 48px; /* Enhanced touch target for better accessibility */
    min-width: 48px;
    padding: 1rem 2rem; /* Increased padding for better touch targets */
    font-size: 1.125rem; /* 18px for better readability */
    font-weight: 600;
    border-radius: 0.75rem; /* Slightly larger radius for modern feel */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 2px solid transparent;
    line-height: 1.4;
    letter-spacing: 0.01em;
    position: relative;
    overflow: hidden;
  }

  /* Enhanced button states */
  button:hover, [role="button"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  button:active, [role="button"]:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  button:focus-visible, [role="button"]:focus-visible {
    outline: 3px solid var(--ring);
    outline-offset: 2px;
  }

  /* Enhanced spacing for components */
  .component-spacing {
    margin: 1.5rem 0;
    padding: 1.5rem;
  }

  .component-spacing-tight {
    margin: 1rem 0;
    padding: 1rem;
  }

  .component-spacing-loose {
    margin: 2rem 0;
    padding: 2rem;
  }
}

/* Enhanced AI State Animations with sophisticated effects */
@keyframes ai-thinking {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.4),
      0 0 40px rgba(59, 130, 246, 0.2),
      inset 0 0 20px rgba(59, 130, 246, 0.1);
    transform: scale(1) rotate(0deg);
  }
  25% {
    box-shadow:
      0 0 25px rgba(59, 130, 246, 0.5),
      0 0 50px rgba(59, 130, 246, 0.3),
      inset 0 0 25px rgba(59, 130, 246, 0.15);
    transform: scale(1.02) rotate(1deg);
  }
  50% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.6),
      0 0 60px rgba(59, 130, 246, 0.4),
      inset 0 0 30px rgba(59, 130, 246, 0.2);
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    box-shadow:
      0 0 25px rgba(59, 130, 246, 0.5),
      0 0 50px rgba(59, 130, 246, 0.3),
      inset 0 0 25px rgba(59, 130, 246, 0.15);
    transform: scale(1.02) rotate(-1deg);
  }
}

@keyframes ai-generating {
  0% {
    box-shadow:
      0 0 15px rgba(124, 58, 237, 0.4),
      0 0 30px rgba(124, 58, 237, 0.2);
    transform: rotate(0deg) scale(1);
  }
  25% {
    box-shadow:
      0 0 20px rgba(124, 58, 237, 0.5),
      0 0 40px rgba(124, 58, 237, 0.3);
    transform: rotate(90deg) scale(1.02);
  }
  50% {
    box-shadow:
      0 0 25px rgba(124, 58, 237, 0.6),
      0 0 50px rgba(124, 58, 237, 0.4);
    transform: rotate(180deg) scale(1.05);
  }
  75% {
    box-shadow:
      0 0 20px rgba(124, 58, 237, 0.5),
      0 0 40px rgba(124, 58, 237, 0.3);
    transform: rotate(270deg) scale(1.02);
  }
  100% {
    box-shadow:
      0 0 15px rgba(124, 58, 237, 0.4),
      0 0 30px rgba(124, 58, 237, 0.2);
    transform: rotate(360deg) scale(1);
  }
}

@keyframes ai-streaming {
  0%, 100% {
    box-shadow: 0 0 15px rgba(5, 150, 105, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(5, 150, 105, 0.6);
  }
}

@keyframes ai-completed {
  0% {
    box-shadow: 0 0 10px rgba(22, 163, 74, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(22, 163, 74, 0.5);
  }
}

.shadow-ai-thinking {
  animation: ai-thinking 2s ease-in-out infinite;
}

.shadow-ai-generating {
  animation: ai-generating 1.2s linear infinite;
}

.shadow-ai-streaming {
  animation: ai-streaming 0.4s ease-in-out infinite;
}

.shadow-ai-completed {
  animation: ai-completed 0.5s ease-out;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .shadow-ai-thinking,
  .shadow-ai-generating,
  .shadow-ai-streaming,
  .shadow-ai-completed {
    animation: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
