@import "tailwindcss";

/* Base styles */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-white text-gray-900;
  }
}

/* MCDesign Custom Color Classes */
/* FSM State Background Colors */
.bg-fsm-defining { background-color: #9ca3af; }
.bg-fsm-onboarding { background-color: #bc96e6; }
.bg-fsm-validating { background-color: #67e8f9; }
.bg-fsm-ready-promote { background-color: #84cc16; }
.bg-fsm-promoting { background-color: #38a3a5; }
.bg-fsm-active { background-color: #10b981; }
.bg-fsm-atom-triggered { background-color: #f59e0b; }
.bg-fsm-subnominal-drift { background-color: #f97316; }
.bg-fsm-degrading { background-color: #ef4444; }
.bg-fsm-quantum-ops { background-color: #8b5cf6; }
.bg-fsm-govern-review { background-color: #06b6d4; }
.bg-fsm-retiring { background-color: #6b7280; }
.bg-fsm-quarantined { background-color: #dc2626; }
.bg-fsm-archived { background-color: #374151; }

/* FSM State Text Colors */
.text-fsm-defining { color: #9ca3af; }
.text-fsm-onboarding { color: #bc96e6; }
.text-fsm-validating { color: #67e8f9; }
.text-fsm-ready-promote { color: #84cc16; }
.text-fsm-promoting { color: #38a3a5; }
.text-fsm-active { color: #10b981; }
.text-fsm-atom-triggered { color: #f59e0b; }
.text-fsm-subnominal-drift { color: #f97316; }
.text-fsm-degrading { color: #ef4444; }
.text-fsm-quantum-ops { color: #8b5cf6; }
.text-fsm-govern-review { color: #06b6d4; }
.text-fsm-retiring { color: #6b7280; }
.text-fsm-quarantined { color: #dc2626; }
.text-fsm-archived { color: #374151; }

/* GAL Level Background Colors */
.bg-gal-0 { background-color: #f3f4f6; }
.bg-gal-1 { background-color: #dbeafe; }
.bg-gal-2 { background-color: #bfdbfe; }
.bg-gal-3 { background-color: #93c5fd; }
.bg-gal-4 { background-color: #60a5fa; }
.bg-gal-5 { background-color: #3b82f6; }

/* GAL Level Text Colors */
.text-gal-0 { color: #6b7280; }
.text-gal-1 { color: #3b82f6; }
.text-gal-2 { color: #2563eb; }
.text-gal-3 { color: #1d4ed8; }
.text-gal-4 { color: #1e40af; }
.text-gal-5 { color: #1e3a8a; }

/* Cognitive Load Background Colors */
.bg-cognitive-low { background-color: #d1fae5; }
.bg-cognitive-medium { background-color: #fef3c7; }
.bg-cognitive-high { background-color: #fed7aa; }
.bg-cognitive-critical { background-color: #fecaca; }

/* Cognitive Load Text Colors */
.text-cognitive-low { color: #065f46; }
.text-cognitive-medium { color: #92400e; }
.text-cognitive-high { color: #c2410c; }
.text-cognitive-critical { color: #dc2626; }

/* AI State Background Colors */
.bg-ai-idle { background-color: #f3f4f6; }
.bg-ai-thinking { background-color: #ddd6fe; }
.bg-ai-generating { background-color: #bfdbfe; }
.bg-ai-streaming { background-color: #a7f3d0; }
.bg-ai-error { background-color: #fecaca; }
.bg-ai-awaiting { background-color: #fef3c7; }
.bg-ai-completed { background-color: #d1fae5; }

/* AI State Text Colors */
.text-ai-idle { color: #6b7280; }
.text-ai-thinking { color: #7c3aed; }
.text-ai-generating { color: #2563eb; }
.text-ai-streaming { color: #059669; }
.text-ai-error { color: #dc2626; }
.text-ai-awaiting { color: #d97706; }
.text-ai-completed { color: #065f46; }

/* Enhanced accessibility classes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.focus-visible-enhanced {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
}

/* Animation classes */
@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

/* Spacing utilities for cognitive load */
.spacing-dense { gap: 0.5rem; }
.spacing-standard { gap: 1rem; }
.spacing-comfortable { gap: 1.5rem; }
.spacing-spacious { gap: 2rem; }
.spacing-accessible { gap: 2.5rem; }
