import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { FSMIndicator, FSMBadge } from '@/components/ui/fsm-indicator';
import { GALIndicator, GALBadge, GALProgress } from '@/components/ui/gal-indicator';
import { CognitiveLoad } from '@/components/ui/cognitive-load';
import { AIIndicator, AIBadge } from '@/components/ui/ai-indicator';

export default function Home() {
  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            MCDesign Token System Integration
          </h1>
          <p className="text-lg text-muted-foreground">
            Demonstrating MCStack principles with Next.js, shadcn/ui, and Tailwind CSS v4
          </p>
        </div>

        {/* MCStack Components Demo */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* FSM States Demo */}
          <Card
            fsmState="active"
            galLevel={3}
            cognitiveLoad={0.4}
            className="p-6"
          >
            <CardHeader>
              <CardTitle>Active Component</CardTitle>
              <CardDescription>
                FSM: Active, GAL: 3, Cognitive Load: 40%
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <FSMIndicator state="active" showLabel />
                <GALIndicator level={3} showDescription />
                <CognitiveLoad value={0.4} showLabel />
              </div>
            </CardContent>
          </Card>

          {/* Quarantined State Demo */}
          <Card
            fsmState="quarantined"
            galLevel={0}
            cognitiveLoad={0.8}
            className="p-6"
          >
            <CardHeader>
              <CardTitle>Quarantined Component</CardTitle>
              <CardDescription>
                FSM: Quarantined, GAL: 0, High Cognitive Load
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <FSMIndicator state="quarantined" showLabel />
                <GALIndicator level={0} showDescription />
                <CognitiveLoad value={0.8} showLabel />
              </div>
            </CardContent>
          </Card>

          {/* AI Component Demo */}
          <Card
            fsmState="active"
            galLevel={5}
            cognitiveLoad={0.3}
            aiState="thinking"
            className="p-6"
          >
            <CardHeader>
              <CardTitle>AI-Powered Component</CardTitle>
              <CardDescription>
                Autonomous AI system with low cognitive load
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <AIIndicator state="thinking" showLabel animate />
                <GALIndicator level={5} showDescription />
                <CognitiveLoad value={0.3} showLabel />
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Indicator Showcase */}
        <Card className="p-6">
          <CardHeader>
            <CardTitle>MCStack Indicators Showcase</CardTitle>
            <CardDescription>
              All available indicators and their variants
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* FSM States */}
            <div>
              <h3 className="text-lg font-semibold mb-3">FSM States</h3>
              <div className="flex flex-wrap gap-2">
                {(['active', 'validating', 'degrading', 'quarantined', 'archived'] as const).map(state => (
                  <FSMBadge key={state} state={state} />
                ))}
              </div>
            </div>

            {/* GAL Levels */}
            <div>
              <h3 className="text-lg font-semibold mb-3">GAL Levels</h3>
              <div className="flex flex-wrap gap-2">
                {([0, 1, 2, 3, 4, 5] as const).map(level => (
                  <GALBadge key={level} level={level} />
                ))}
              </div>
              <div className="mt-4">
                <GALProgress level={3} showLabels />
              </div>
            </div>

            {/* Cognitive Load */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Cognitive Load</h3>
              <div className="space-y-3">
                <CognitiveLoad value={0.2} label="Low Load" showValue />
                <CognitiveLoad value={0.5} label="Medium Load" showValue />
                <CognitiveLoad value={0.8} label="High Load" showValue />
                <CognitiveLoad value={0.95} label="Critical Load" showValue />
              </div>
            </div>

            {/* AI States */}
            <div>
              <h3 className="text-lg font-semibold mb-3">AI States</h3>
              <div className="flex flex-wrap gap-2">
                {(['idle', 'thinking', 'generating', 'streaming', 'error', 'completed'] as const).map(state => (
                  <AIBadge key={state} state={state} />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
