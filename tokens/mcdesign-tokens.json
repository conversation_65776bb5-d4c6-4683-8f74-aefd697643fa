{"color": {"brand": {"deepBlue": "#00152b", "teal": "#38a3a5", "lavenderPurple": "#bc96e6", "darkRed": "#8a0b15", "lightSeaGreen": "#2ec4b6", "brightYellow": "#fcca46", "richBlack": "#011627", "darkerSlateGray": "#2e3347", "saturatedSlateGray": "#4a4e6d", "offWhite": "#f8f8ff", "silver": "#c0c0c0", "white": "#FFFFFF"}, "semantic": {"primary": "{color.brand.teal}", "secondary": "{color.brand.lavenderPurple}", "accent": "{color.brand.brightYellow}", "background": {"default": "{color.brand.offWhite}", "dark": "{color.brand.richBlack}", "subtle": "#f5f7fa"}, "text": {"primary": "{color.brand.richBlack}", "secondary": "{color.brand.darkerSlateGray}", "muted": "{color.brand.saturatedSlateGray}", "inverse": "{color.brand.offWhite}"}}, "light": {"background": {"primary": "#ffffff", "secondary": "#f8fafc", "tertiary": "#f1f5f9", "elevated": "#ffffff"}, "text": {"primary": "#0f172a", "secondary": "#334155", "tertiary": "#64748b", "inverse": "#ffffff"}, "border": {"primary": "#e2e8f0", "secondary": "#cbd5e1", "focus": "#3b82f6"}}, "dark": {"background": {"primary": "#0f172a", "secondary": "#1e293b", "tertiary": "#334155", "elevated": "#1e293b"}, "text": {"primary": "#f8fafc", "secondary": "#cbd5e1", "tertiary": "#94a3b8", "inverse": "#0f172a"}, "border": {"primary": "#334155", "secondary": "#475569", "focus": "#60a5fa"}}, "fsm": {"defining": "#9ca3af", "onboarding": "#bc96e6", "validating": "#67e8f9", "ready_promote": "#84cc16", "promoting": "#38a3a5", "active": "#10b981", "atom_triggered": "#fcca46", "subnominal_drift": "#f97316", "degrading": "#b45309", "quantum_ops": "#8b5cf6", "govern_review": "#6366f1", "retiring": "#f97316", "quarantined": "#8a0b15", "archived": "#475569"}, "gal": {"gal0": "#9ca3af", "gal1": "#60a5fa", "gal2": "#34d399", "gal3": "#fbbf24", "gal4": "#f97316", "gal5": "#ef4444"}, "cognitive": {"load": {"low": "#34d399", "medium": "#fbbf24", "high": "#f97316", "critical": "#ef4444"}, "confidence": {"certain": "#10b981", "likely": "#60a5fa", "uncertain": "#fbbf24", "unknown": "#9ca3af"}}, "ai": {"idle": "#64748b", "thinking": "#3b82f6", "generating": "#7c3aed", "streaming": "#059669", "error": "#dc2626", "awaiting": "#d97706", "completed": "#16a34a", "light": {"idle": "#64748b", "thinking": "#2563eb", "generating": "#7c3aed", "streaming": "#059669", "error": "#dc2626", "awaiting": "#d97706", "completed": "#16a34a"}, "dark": {"idle": "#94a3b8", "thinking": "#60a5fa", "generating": "#a855f7", "streaming": "#10b981", "error": "#f87171", "awaiting": "#fbbf24", "completed": "#4ade80"}}}, "typography": {"fontFamily": {"heading": "Inter, system-ui, -apple-system, sans-serif", "body": "Inter, system-ui, -apple-system, sans-serif", "mono": "\"JetBrains Mono\", \"Fira Code\", Consolas, monospace"}, "fontSize": {"xs": "0.8125rem", "sm": "0.9375rem", "base": "1.0625rem", "lg": "1.1875rem", "xl": "1.3125rem", "2xl": "1.5625rem", "3xl": "1.9375rem", "4xl": "2.3125rem", "5xl": "3.0625rem", "6xl": "3.8125rem"}, "fontWeight": {"light": "300", "regular": "400", "medium": "500", "semibold": "600", "bold": "700", "extrabold": "800"}, "lineHeight": {"none": "1", "tight": "1.3", "snug": "1.4", "normal": "1.6", "relaxed": "1.7", "loose": "2.0"}, "letterSpacing": {"tighter": "-0.05em", "tight": "-0.025em", "normal": "0", "wide": "0.025em", "wider": "0.05em", "widest": "0.1em"}, "wcag": {"minFontSize": "1rem", "minLineHeight": "1.5", "minLetterSpacing": "0.12em", "focusOutlineWidth": "3px", "touchTargetSize": "44px"}, "cognitive": {"readability": {"standard": "1.0625rem", "enhanced": "1.25rem", "accessible": "1.5rem"}, "emphasis": {"subtle": "0.5", "standard": "1", "strong": "1.5"}}}, "spacing": {"0": "0", "1": "0.375rem", "2": "0.5625rem", "3": "0.8125rem", "4": "1.125rem", "5": "1.375rem", "6": "1.6875rem", "8": "2.25rem", "10": "2.8125rem", "12": "3.375rem", "16": "4.5rem", "20": "5.625rem", "24": "6.75rem", "32": "9rem", "wcag": {"touchTarget": "2.75rem", "focusOffset": "0.125rem", "textSpacing": "0.16em"}, "cognitive": {"dense": "0.8125rem", "standard": "1.125rem", "comfortable": "1.6875rem", "spacious": "2.25rem", "accessible": "2.8125rem"}}, "radii": {"none": "0", "sm": "0.125rem", "md": "0.25rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "3xl": "1.5rem", "full": "9999px"}, "shadow": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)", "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)", "inner": "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)", "fsm": {"active": "0 4px 6px -1px rgba(16, 185, 129, 0.2)", "quarantined": "0 4px 6px -1px rgba(138, 11, 21, 0.3)", "validating": "0 4px 6px -1px rgba(103, 232, 249, 0.2)"}}, "animation": {"duration": {"fast": "150ms", "normal": "300ms", "slow": "500ms", "slower": "800ms", "ai": {"thinking": "2000ms", "generating": "1200ms", "streaming": "400ms", "pulse": "1500ms", "breathe": "3000ms", "ripple": "2500ms"}, "cognitive": {"attention": "500ms", "processing": "900ms", "focus": "300ms"}, "accessibility": {"focus": "200ms", "hover": "150ms", "reduced": "0ms"}}, "easing": {"standard": "cubic-bezier(0.4, 0, 0.2, 1)", "accelerate": "cubic-bezier(0.4, 0, 1, 1)", "decelerate": "cubic-bezier(0, 0, 0.2, 1)", "sharp": "cubic-bezier(0.4, 0, 0.6, 1)", "spring": "cubic-bezier(0.175, 0.885, 0.32, 1.275)", "ai": {"thinking": "cubic-bezier(0.4, 0, 0.2, 1)", "generating": "cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}}, "opacity": {"0": "0", "5": "0.05", "10": "0.1", "20": "0.2", "25": "0.25", "30": "0.3", "40": "0.4", "50": "0.5", "60": "0.6", "70": "0.7", "75": "0.75", "80": "0.8", "90": "0.9", "95": "0.95", "100": "1", "fsm": {"archived": "0.6", "quarantined": "0.85", "degrading": "0.9"}}, "zIndex": {"0": "0", "10": "10", "20": "20", "30": "30", "40": "40", "50": "50", "auto": "auto", "fsm": {"indicator": "100"}}, "border": {"width": {"0": "0px", "1": "1px", "2": "2px", "4": "4px", "8": "8px"}, "style": {"solid": "solid", "dashed": "dashed", "dotted": "dotted", "double": "double", "none": "none"}, "fsm": {"quarantined": {"style": "dashed", "width": "2px", "color": "{color.fsm.quarantined}"}, "validating": {"style": "dotted", "width": "1px", "color": "{color.fsm.validating}"}}}, "breakpoint": {"xs": "320px", "sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "focus": {"standard": "0 0 0 3px rgba(56, 163, 165, 0.5)", "warning": "0 0 0 3px rgba(251, 191, 36, 0.5)", "error": "0 0 0 3px rgba(239, 68, 68, 0.5)", "cognitive": {"assistive": "0 0 0 4px rgba(56, 163, 165, 0.7)"}}, "transition": {"property": {"common": "background-color, border-color, color, fill, stroke, opacity, box-shadow, transform", "colors": "background-color, border-color, color, fill, stroke", "opacity": "opacity", "shadow": "box-shadow", "transform": "transform", "ai": "opacity, background-color, transform, box-shadow"}, "timing": {"standard": "{animation.duration.normal} {animation.easing.standard}", "fast": "{animation.duration.fast} {animation.easing.standard}", "slow": "{animation.duration.slow} {animation.easing.standard}", "fsm": {"change": "{animation.duration.normal} {animation.easing.standard}"}, "ai": {"thinking": "{animation.duration.ai.thinking} {animation.easing.ai.thinking}", "generating": "{animation.duration.ai.generating} {animation.easing.ai.generating}"}}}, "cognitive": {"load": {"threshold": {"low": "0.3", "medium": "0.6", "high": "0.8", "critical": "0.9"}}, "timing": {"attention": "400ms", "processing": "800ms"}, "simplification": {"minimal": "0.9", "moderate": "0.7", "significant": "0.5"}}, "component": {"button": {"height": {"sm": "2rem", "md": "2.5rem", "lg": "3rem"}, "padding": {"sm": "0.5rem 0.75rem", "md": "0.75rem 1rem", "lg": "1rem 1.5rem"}, "radii": "{radii.md}", "border": "{border.width.1}", "cognitive": {"load": "0.3", "safe": "0.2", "caution": "0.5", "danger": "0.8"}}, "card": {"padding": "{spacing.6}", "radii": "{radii.lg}", "shadow": "{shadow.md}", "border": "{border.width.1}", "fsm": {"indicator": {"size": "1.5rem", "position": "top-right"}}, "cognitive": {"load": "0.4"}}, "input": {"height": "2.5rem", "padding": "0.5rem 0.75rem", "radii": "{radii.md}", "border": "{border.width.1}", "cognitive": {"load": "0.5"}}, "aiResponse": {"border": "{border.width.1}", "radii": "{radii.lg}", "padding": "{spacing.6}", "indicator": {"size": "1rem", "position": "top-left"}, "cognitive": {"load": "0.7"}}, "fsmIndicator": {"size": "1.5rem", "radii": "{radii.full}", "border": "{border.width.0}"}, "galIndicator": {"size": "1.25rem", "radii": "{radii.full}", "border": "{border.width.0}"}, "confidenceIndicator": {"height": "0.5rem", "radii": "{radii.full}", "border": "{border.width.0}"}, "cognitiveLoadIndicator": {"height": "0.5rem", "radii": "{radii.full}", "border": "{border.width.0}"}}}