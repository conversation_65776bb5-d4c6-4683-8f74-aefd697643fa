# MCDesign Token System Integration Guide

## Overview

This guide covers the integration of the MCDesign Token System with Next.js, shadcn/ui, and Tailwind CSS v4, following MCStack principles for safe, governable, and explainable UI development.

## Architecture

### Token Processing System

The integration uses a sophisticated token processing system that:

- **Resolves token references** in the format `{path.to.token}`
- **Generates CSS custom properties** from MCDesign tokens
- **Provides type-safe access** to token values
- **Supports runtime token manipulation**

### Key Components

1. **Token Resolver** (`lib/design-tokens.ts`)
2. **MCDesign Provider** (`components/providers/mcdesign-provider.tsx`)
3. **MCStack Components** (FSM, GAL, Cognitive Load, AI indicators)
4. **Enhanced shadcn/ui Components** with MCStack features
5. **Utility Library** (`lib/mcstack.ts`)

## Usage Examples

### Basic Component with MCStack Features

```tsx
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

function MyComponent() {
  return (
    <Card 
      fsmState="active"
      galLevel={3}
      cognitiveLoad={0.4}
      showIndicators={true}
    >
      <CardHeader>
        <CardTitle>MCStack Enhanced Card</CardTitle>
      </CardHeader>
      <CardContent>
        This card automatically displays FSM state, GAL level, and cognitive load indicators.
      </CardContent>
    </Card>
  );
}
```

### Using Individual Indicators

```tsx
import { FSMIndicator } from '@/components/ui/fsm-indicator';
import { GALIndicator } from '@/components/ui/gal-indicator';
import { CognitiveLoad } from '@/components/ui/cognitive-load';
import { AIIndicator } from '@/components/ui/ai-indicator';

function IndicatorExample() {
  return (
    <div className="flex gap-4 items-center">
      <FSMIndicator state="active" showLabel />
      <GALIndicator level={3} showDescription />
      <CognitiveLoad value={0.6} showLabel />
      <AIIndicator state="thinking" animate />
    </div>
  );
}
```

### Using MCStack Utilities

```tsx
import { MCStack } from '@/lib/mcstack';

function UtilityExample() {
  // Create configuration for different component types
  const safeConfig = MCStack.config('safe');
  const autonomousConfig = MCStack.config('autonomous', {
    cognitiveLoad: 0.3 // Override default
  });
  
  // Calculate cognitive load for complex interfaces
  const totalLoad = MCStack.cognitiveLoad.calculate([
    { type: 'form', count: 1 },
    { type: 'input', count: 5 },
    { type: 'button', count: 3 }
  ]);
  
  // Get recommended GAL level
  const recommendedGAL = MCStack.gal.getRecommended(
    'high',        // complexity
    'medium',      // risk level
    'intermediate' // user experience
  );
  
  return (
    <Card {...safeConfig}>
      <CardContent>
        Recommended GAL Level: {recommendedGAL}
        <br />
        Calculated Cognitive Load: {totalLoad.toFixed(2)}
      </CardContent>
    </Card>
  );
}
```

## Token Categories

### FSM (Finite State Machine) States

- `defining` - Component being defined
- `onboarding` - In onboarding phase
- `validating` - Undergoing validation
- `ready_promote` - Ready for promotion
- `promoting` - Being promoted
- `active` - Active and operational
- `atom_triggered` - Triggered by atom
- `subnominal_drift` - Experiencing drift
- `degrading` - Performance degrading
- `quantum_ops` - Quantum operations mode
- `govern_review` - Under governance review
- `retiring` - Being retired
- `quarantined` - Quarantined due to issues
- `archived` - Archived

### GAL (Governed Autonomy Levels)

- **GAL 0** - No autonomy, full human control
- **GAL 1** - Minimal autonomy, human approval required
- **GAL 2** - Low autonomy, human approval for significant actions
- **GAL 3** - Medium autonomy, human oversight with intervention
- **GAL 4** - High autonomy, human monitoring with exceptions
- **GAL 5** - Full autonomy, minimal human intervention

### Cognitive Load Thresholds

- **Low** (0-0.3) - Minimal cognitive effort
- **Medium** (0.3-0.6) - Moderate cognitive effort
- **High** (0.6-0.8) - High cognitive effort
- **Critical** (0.8-1.0) - Critical load, needs simplification

### AI States

- `idle` - AI system ready
- `thinking` - Processing and analyzing
- `generating` - Generating content
- `streaming` - Streaming output
- `error` - Error encountered
- `awaiting` - Waiting for input
- `completed` - Task completed

## CSS Custom Properties

The system automatically generates CSS custom properties for all tokens:

```css
/* FSM States */
--color-fsm-active: #10b981;
--color-fsm-quarantined: #8a0b15;

/* GAL Levels */
--color-gal-0: #9ca3af;
--color-gal-5: #ef4444;

/* Cognitive Load */
--color-cognitive-load-low: #34d399;
--color-cognitive-load-critical: #ef4444;

/* AI States */
--color-ai-thinking: #60a5fa;
--color-ai-error: #ef4444;
```

## Tailwind Integration

The tokens are integrated with Tailwind CSS v4 through the `@theme` directive:

```css
@theme inline {
  /* MCDesign FSM State Colors */
  --color-fsm-active: var(--color-fsm-active);
  --color-fsm-quarantined: var(--color-fsm-quarantined);
  
  /* GAL Level Colors */
  --color-gal-0: var(--color-gal-gal0);
  --color-gal-5: var(--color-gal-gal5);
}
```

Use in classes:

```tsx
<div className="bg-fsm-active text-white">Active State</div>
<div className="bg-gal-3 text-black">GAL Level 3</div>
<div className="bg-cognitive-load-high">High Cognitive Load</div>
```

## Best Practices

### 1. Component Design

- Always consider cognitive load when designing interfaces
- Use appropriate GAL levels based on user expertise and risk
- Implement FSM states for stateful components
- Show AI states for AI-powered features

### 2. Accessibility

- All indicators include proper ARIA labels
- Color is not the only means of conveying information
- Tooltips provide detailed descriptions
- Keyboard navigation is supported

### 3. Performance

- Tokens are applied once at application startup
- CSS custom properties enable efficient runtime updates
- Components use memoization where appropriate

### 4. Governance

- FSM state transitions follow defined rules
- GAL level changes require validation
- Cognitive load thresholds trigger warnings
- All changes are logged for audit trails

## Advanced Features

### Runtime Token Updates

```tsx
import { setCSSVariable } from '@/components/providers/mcdesign-provider';

// Update a token value at runtime
setCSSVariable('--color-fsm-active', '#new-color');
```

### Custom Token Extensions

```tsx
const customTokens = {
  '--my-custom-color': '#ff0000',
  '--my-custom-spacing': '2rem'
};

<MCDesignProvider customVariables={customTokens}>
  <App />
</MCDesignProvider>
```

### Validation and Safety

```tsx
import { mcstackValidation } from '@/lib/mcstack';

// Validate before state changes
if (mcstackValidation.isValidFSMTransition('active', 'degrading')) {
  // Safe to transition
}

if (mcstackValidation.isValidCognitiveLoad(0.5)) {
  // Valid cognitive load value
}
```

## Troubleshooting

### Common Issues

1. **Tokens not applying**: Ensure MCDesignProvider wraps your app
2. **Colors not showing**: Check that token references are resolved correctly
3. **TypeScript errors**: Verify imports and type definitions
4. **Performance issues**: Use React.memo for frequently updating components

### Debug Mode

Enable debug logging in development:

```tsx
<MCDesignProvider applyTokens={true}>
  <App />
</MCDesignProvider>
```

Check browser console for token application logs.

## Migration Guide

### From Existing shadcn/ui

1. Wrap your app with `MCDesignProvider`
2. Update component imports to use enhanced versions
3. Add MCStack props to existing components
4. Test token application and indicator display

### From Other Design Systems

1. Map existing tokens to MCDesign structure
2. Update color references to use new token names
3. Implement MCStack indicators gradually
4. Validate cognitive load calculations

## Contributing

When extending the system:

1. Follow MCStack principles for safety and governance
2. Add proper TypeScript types
3. Include accessibility features
4. Document new tokens and components
5. Add validation rules for new states/levels
6. Update this documentation

## Support

For issues and questions:

1. Check this documentation first
2. Review component source code
3. Check browser console for errors
4. Validate token structure and references
5. Test with minimal reproduction case
