# UI Prototype Builder

This project allows you to create advanced UI prototypes effortlessly using GitHub Copilot in VS Code's Agent mode. Built with shadcn, React, and Next.js, you can develop sophisticated interfaces without the need for complex prompts. Boilerplate project includes:

- Next.js application
- shadcn/ui component library
- Tailwind styling
- lucide-react icons

## Getting Started

1. Clone this repository to get the template React project.

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Run an example prompt. Turn on "Agent mode" in Copilot Chat and prompt with an example:

```bash
Make a dashboard with mock data. it has 3 columns, with the middle one as the main one, left is nav, and right is for supplementary / smaller information. it should have various modules, and small charts showcasing some simulated scenario. it should be clear and have interactive elements.
```

**Optional:** Enable 'auto approve' in Copilot settings to automatically apply agent-written changes to the codebase. You'll still be able to review and choose whether to keep or discard changes from the entire session. This setting can be found in VS Code settings > Chat > Tools > Auto Approve.


## Example

See the simple prompt in the GitHub Copilot chat, and the web preview that was generated.
<img width="1768" alt="Screenshot 2025-06-04 at 11 39 10 AM" src="https://github.com/user-attachments/assets/d783e75a-477c-4298-87c9-e13284cf5b3e" />
