import { 
  getToken, 
  getFSMColor, 
  getGALColor, 
  getCognitiveLoadColor, 
  getAIColor 
} from './design-tokens';
import type { FSMState } from '@/components/ui/fsm-indicator';
import type { GALLevel } from '@/components/ui/gal-indicator';
import type { AIState } from '@/components/ui/ai-indicator';

/**
 * MCStack Utility Library
 * 
 * Provides utilities for working with MCStack principles including
 * FSM states, GAL levels, cognitive load, and AI state management.
 */

// Re-export types for convenience
export type { FSMState, GALLevel, AIState };

/**
 * MCStack Component Configuration
 */
export interface MCStackConfig {
  fsmState?: FSMState;
  galLevel?: GALLevel;
  cognitiveLoad?: number;
  aiState?: AIState;
  showIndicators?: boolean;
  indicatorPosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

/**
 * Default cognitive load values for common UI patterns
 */
export const DEFAULT_COGNITIVE_LOADS = {
  button: 0.2,
  input: 0.3,
  form: 0.5,
  card: 0.4,
  modal: 0.6,
  table: 0.7,
  dashboard: 0.8,
  complexForm: 0.9
} as const;

/**
 * FSM State transition rules for validation
 */
export const FSM_TRANSITIONS: Record<FSMState, FSMState[]> = {
  defining: ['onboarding', 'archived'],
  onboarding: ['validating', 'quarantined'],
  validating: ['ready_promote', 'degrading', 'quarantined'],
  ready_promote: ['promoting', 'validating'],
  promoting: ['active', 'degrading'],
  active: ['atom_triggered', 'degrading', 'retiring'],
  atom_triggered: ['active', 'subnominal_drift'],
  subnominal_drift: ['active', 'degrading'],
  degrading: ['quarantined', 'retiring', 'active'],
  quantum_ops: ['active', 'govern_review'],
  govern_review: ['active', 'quarantined', 'retiring'],
  retiring: ['archived', 'quarantined'],
  quarantined: ['validating', 'archived'],
  archived: ['defining']
};

/**
 * GAL Level progression rules
 */
export const GAL_PROGRESSION: Record<GALLevel, GALLevel[]> = {
  0: [1],
  1: [0, 2],
  2: [1, 3],
  3: [2, 4],
  4: [3, 5],
  5: [4]
};

/**
 * Validate FSM state transition
 */
export function isValidFSMTransition(from: FSMState, to: FSMState): boolean {
  return FSM_TRANSITIONS[from]?.includes(to) ?? false;
}

/**
 * Validate GAL level progression
 */
export function isValidGALProgression(from: GALLevel, to: GALLevel): boolean {
  return GAL_PROGRESSION[from]?.includes(to) ?? false;
}

/**
 * Calculate cognitive load for a component tree
 */
export function calculateCognitiveLoad(components: Array<{ type: keyof typeof DEFAULT_COGNITIVE_LOADS; count?: number }>): number {
  const totalLoad = components.reduce((sum, component) => {
    const baseLoad = DEFAULT_COGNITIVE_LOADS[component.type];
    const count = component.count || 1;
    return sum + (baseLoad * count);
  }, 0);
  
  // Apply diminishing returns for multiple components
  return Math.min(1, totalLoad * 0.8);
}

/**
 * Get recommended GAL level based on component complexity and risk
 */
export function getRecommendedGAL(
  complexity: 'low' | 'medium' | 'high',
  riskLevel: 'low' | 'medium' | 'high',
  userExperience: 'novice' | 'intermediate' | 'expert'
): GALLevel {
  const complexityScore = { low: 1, medium: 2, high: 3 }[complexity];
  const riskScore = { low: 1, medium: 2, high: 3 }[riskLevel];
  const experienceScore = { novice: 1, intermediate: 2, expert: 3 }[userExperience];
  
  const totalScore = complexityScore + riskScore - experienceScore;
  
  if (totalScore <= 1) return 4;
  if (totalScore <= 2) return 3;
  if (totalScore <= 3) return 2;
  if (totalScore <= 4) return 1;
  return 0;
}

/**
 * MCStack component factory with default configurations
 */
export function createMCStackConfig(
  type: 'safe' | 'standard' | 'autonomous' | 'critical',
  overrides: Partial<MCStackConfig> = {}
): MCStackConfig {
  const baseConfigs = {
    safe: {
      fsmState: 'active' as FSMState,
      galLevel: 1 as GALLevel,
      cognitiveLoad: 0.3,
      showIndicators: true
    },
    standard: {
      fsmState: 'active' as FSMState,
      galLevel: 3 as GALLevel,
      cognitiveLoad: 0.5,
      showIndicators: true
    },
    autonomous: {
      fsmState: 'active' as FSMState,
      galLevel: 5 as GALLevel,
      cognitiveLoad: 0.4,
      showIndicators: true
    },
    critical: {
      fsmState: 'govern_review' as FSMState,
      galLevel: 0 as GALLevel,
      cognitiveLoad: 0.8,
      showIndicators: true
    }
  };
  
  return { ...baseConfigs[type], ...overrides };
}

/**
 * Generate CSS classes for MCStack styling
 */
export function getMCStackClasses(config: MCStackConfig): string {
  const classes: string[] = [];
  
  if (config.fsmState) {
    classes.push(`fsm-${config.fsmState.replace('_', '-')}`);
    
    // Add state-specific classes
    if (config.fsmState === 'quarantined') {
      classes.push('border-dashed', 'border-2');
    }
    if (config.fsmState === 'archived') {
      classes.push('opacity-60');
    }
    if (config.fsmState === 'degrading') {
      classes.push('opacity-90');
    }
  }
  
  if (config.galLevel !== undefined) {
    classes.push(`gal-${config.galLevel}`);
  }
  
  if (config.cognitiveLoad !== undefined) {
    if (config.cognitiveLoad > 0.8) {
      classes.push('ring-2', 'ring-cognitive-load-high/20');
    }
  }
  
  return classes.join(' ');
}

/**
 * MCStack theme utilities
 */
export const mcstackTheme = {
  /**
   * Get color value for any MCStack token
   */
  getColor: (path: string) => getToken(path),
  
  /**
   * Get FSM state color
   */
  getFSMColor,
  
  /**
   * Get GAL level color
   */
  getGALColor,
  
  /**
   * Get cognitive load color
   */
  getCognitiveLoadColor,
  
  /**
   * Get AI state color
   */
  getAIColor,
  
  /**
   * Get semantic colors
   */
  semantic: {
    primary: () => getToken('color.semantic.primary'),
    secondary: () => getToken('color.semantic.secondary'),
    accent: () => getToken('color.semantic.accent')
  },
  
  /**
   * Get spacing values
   */
  spacing: {
    cognitive: {
      dense: () => getToken('spacing.cognitive.dense'),
      standard: () => getToken('spacing.cognitive.standard'),
      comfortable: () => getToken('spacing.cognitive.comfortable'),
      spacious: () => getToken('spacing.cognitive.spacious')
    }
  }
};

/**
 * MCStack validation utilities
 */
export const mcstackValidation = {
  /**
   * Validate cognitive load value
   */
  isValidCognitiveLoad: (value: number): boolean => value >= 0 && value <= 1,
  
  /**
   * Validate GAL level
   */
  isValidGALLevel: (level: number): level is GALLevel => 
    Number.isInteger(level) && level >= 0 && level <= 5,
  
  /**
   * Validate FSM state transition
   */
  isValidFSMTransition,
  
  /**
   * Validate GAL progression
   */
  isValidGALProgression
};

/**
 * Export all utilities as a single namespace
 */
export const MCStack = {
  config: createMCStackConfig,
  theme: mcstackTheme,
  validation: mcstackValidation,
  cognitiveLoad: {
    defaults: DEFAULT_COGNITIVE_LOADS,
    calculate: calculateCognitiveLoad
  },
  gal: {
    getRecommended: getRecommendedGAL,
    progression: GAL_PROGRESSION
  },
  fsm: {
    transitions: FSM_TRANSITIONS
  },
  utils: {
    getClasses: getMCStackClasses
  }
};
