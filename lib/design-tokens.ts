import mcdesignTokens from '@/tokens/mcdesign-tokens.json';

// Type definitions for token structure
export type TokenValue = string | number | Record<string, any>;
export type TokenPath = string;

/**
 * Resolves token references in the format {path.to.token}
 * @param value - The token value that might contain references
 * @param tokens - The token object to resolve from
 * @returns Resolved token value
 */
function resolveTokenReference(value: TokenValue, tokens: any = mcdesignTokens): string {
  if (typeof value !== 'string') {
    return String(value);
  }

  // Check if it's a token reference
  if (value.startsWith('{') && value.endsWith('}')) {
    const path = value.slice(1, -1); // Remove { and }
    const parts = path.split('.');
    let current = tokens;

    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        console.warn(`Token reference not found: ${path}`);
        return value; // Return original if not found
      }
    }

    // Recursively resolve if the result is also a reference
    return resolveTokenReference(current, tokens);
  }

  return value;
}

/**
 * Generates CSS custom properties from MCDesign tokens
 * @returns Object with CSS variable names and resolved values
 */
export function generateCssVariables(): Record<string, string> {
  const cssVars: Record<string, string> = {};

  // Process color tokens
  const processColorTokens = (colorObj: any, prefix: string = 'color') => {
    Object.entries(colorObj).forEach(([category, values]) => {
      if (typeof values === 'object' && values !== null) {
        Object.entries(values).forEach(([key, value]) => {
          const resolvedValue = resolveTokenReference(value);
          cssVars[`--${prefix}-${category}-${key}`] = resolvedValue;
        });
      }
    });
  };

  // Process colors
  processColorTokens(mcdesignTokens.color);

  // Process typography tokens
  Object.entries(mcdesignTokens.typography).forEach(([category, values]) => {
    if (typeof values === 'object' && values !== null) {
      Object.entries(values).forEach(([key, value]) => {
        const resolvedValue = resolveTokenReference(value);
        cssVars[`--typography-${category}-${key}`] = resolvedValue;
      });
    }
  });

  // Process spacing tokens
  Object.entries(mcdesignTokens.spacing).forEach(([key, value]) => {
    if (key !== 'cognitive') {
      const resolvedValue = resolveTokenReference(value);
      cssVars[`--spacing-${key}`] = resolvedValue;
    }
  });

  // Process cognitive spacing
  if (mcdesignTokens.spacing.cognitive) {
    Object.entries(mcdesignTokens.spacing.cognitive).forEach(([key, value]) => {
      const resolvedValue = resolveTokenReference(value);
      cssVars[`--spacing-cognitive-${key}`] = resolvedValue;
    });
  }

  // Process radii tokens
  Object.entries(mcdesignTokens.radii).forEach(([key, value]) => {
    const resolvedValue = resolveTokenReference(value);
    cssVars[`--radii-${key}`] = resolvedValue;
  });

  // Process shadow tokens
  const processShadowTokens = (shadowObj: any, prefix: string = 'shadow') => {
    Object.entries(shadowObj).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          const resolvedValue = resolveTokenReference(subValue);
          cssVars[`--${prefix}-${key}-${subKey}`] = resolvedValue;
        });
      } else {
        const resolvedValue = resolveTokenReference(value);
        cssVars[`--${prefix}-${key}`] = resolvedValue;
      }
    });
  };

  processShadowTokens(mcdesignTokens.shadow);

  // Process animation tokens
  Object.entries(mcdesignTokens.animation).forEach(([category, values]) => {
    if (typeof values === 'object' && values !== null) {
      Object.entries(values).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          Object.entries(value).forEach(([subKey, subValue]) => {
            const resolvedValue = resolveTokenReference(subValue);
            cssVars[`--animation-${category}-${key}-${subKey}`] = resolvedValue;
          });
        } else {
          const resolvedValue = resolveTokenReference(value);
          cssVars[`--animation-${category}-${key}`] = resolvedValue;
        }
      });
    }
  });

  // Process opacity tokens
  Object.entries(mcdesignTokens.opacity).forEach(([key, value]) => {
    if (typeof value === 'object' && value !== null) {
      Object.entries(value).forEach(([subKey, subValue]) => {
        const resolvedValue = resolveTokenReference(subValue);
        cssVars[`--opacity-${key}-${subKey}`] = resolvedValue;
      });
    } else {
      const resolvedValue = resolveTokenReference(value);
      cssVars[`--opacity-${key}`] = resolvedValue;
    }
  });

  // Process cognitive load thresholds
  if (mcdesignTokens.cognitive?.load?.threshold) {
    Object.entries(mcdesignTokens.cognitive.load.threshold).forEach(([key, value]) => {
      const resolvedValue = resolveTokenReference(value);
      cssVars[`--cognitive-load-threshold-${key}`] = resolvedValue;
    });
  }

  // Process component tokens
  Object.entries(mcdesignTokens.component).forEach(([componentName, componentTokens]) => {
    if (typeof componentTokens === 'object' && componentTokens !== null) {
      const processComponentTokens = (tokens: any, prefix: string) => {
        Object.entries(tokens).forEach(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            processComponentTokens(value, `${prefix}-${key}`);
          } else {
            const resolvedValue = resolveTokenReference(value);
            cssVars[`--component-${componentName}${prefix}-${key}`] = resolvedValue;
          }
        });
      };
      processComponentTokens(componentTokens, '');
    }
  });

  return cssVars;
}

/**
 * Type-safe token access utility
 * @param path - Dot-separated path to token (e.g., 'color.brand.teal')
 * @returns Resolved token value
 */
export function getToken(path: TokenPath): string {
  const parts = path.split('.');
  let current: any = mcdesignTokens;

  for (const part of parts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part];
    } else {
      console.warn(`Token not found: ${path}`);
      return '';
    }
  }

  return resolveTokenReference(current);
}

/**
 * Get FSM state color
 * @param state - FSM state name
 * @returns CSS color value
 */
export function getFSMColor(state: keyof typeof mcdesignTokens.color.fsm): string {
  return getToken(`color.fsm.${state}`);
}

/**
 * Get GAL level color
 * @param level - GAL level (0-5)
 * @returns CSS color value
 */
export function getGALColor(level: keyof typeof mcdesignTokens.color.gal): string {
  return getToken(`color.gal.${level}`);
}

/**
 * Get cognitive load color based on threshold
 * @param value - Load value (0-1)
 * @returns CSS color value
 */
export function getCognitiveLoadColor(value: number): string {
  const thresholds = mcdesignTokens.cognitive.load.threshold;
  
  if (value >= Number(thresholds.critical)) return getToken('color.cognitive.load.critical');
  if (value >= Number(thresholds.high)) return getToken('color.cognitive.load.high');
  if (value >= Number(thresholds.medium)) return getToken('color.cognitive.load.medium');
  return getToken('color.cognitive.load.low');
}

/**
 * Get AI state color
 * @param state - AI state name
 * @returns CSS color value
 */
export function getAIColor(state: keyof typeof mcdesignTokens.color.ai): string {
  return getToken(`color.ai.${state}`);
}
