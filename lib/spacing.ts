

/**
 * WCAG 3.0 Compliant Spacing System
 * 
 * Provides consistent spacing utilities that meet accessibility guidelines
 * for touch targets, text spacing, and cognitive load management.
 */

// Base spacing scale (in rem units for accessibility)
export const SPACING_SCALE = {
  0: '0',
  1: '0.375rem',    // 6px
  2: '0.5625rem',   // 9px  
  3: '0.8125rem',   // 13px
  4: '1.125rem',    // 18px
  5: '1.375rem',    // 22px
  6: '1.6875rem',   // 27px
  8: '2.25rem',     // 36px
  10: '2.8125rem',  // 45px
  12: '3.375rem',   // 54px
  16: '4.5rem',     // 72px
  20: '5.625rem',   // 90px
  24: '6.75rem',    // 108px
  32: '9rem'        // 144px
} as const;

// WCAG 3.0 specific spacing requirements
export const WCAG_SPACING = {
  // Minimum touch target size (44px)
  touchTarget: '2.75rem',
  
  // Focus outline offset
  focusOffset: '0.125rem',
  
  // Text spacing for readability
  textSpacing: '0.16em',
  
  // Line height minimum
  lineHeightMin: 1.5,
  
  // Letter spacing minimum for body text
  letterSpacingMin: '0.12em'
} as const;

// Cognitive load based spacing
export const COGNITIVE_SPACING = {
  dense: '0.8125rem',      // High information density
  standard: '1.125rem',    // Normal spacing
  comfortable: '1.6875rem', // Relaxed spacing
  spacious: '2.25rem',     // Maximum comfort
  accessible: '2.8125rem'  // Enhanced accessibility
} as const;

/**
 * Get spacing value by key
 */
export function getSpacing(key: keyof typeof SPACING_SCALE): string {
  return SPACING_SCALE[key];
}

/**
 * Get cognitive spacing value
 */
export function getCognitiveSpacing(level: keyof typeof COGNITIVE_SPACING): string {
  return COGNITIVE_SPACING[level];
}

/**
 * Get WCAG compliant spacing value
 */
export function getWCAGSpacing(key: keyof typeof WCAG_SPACING): string | number {
  return WCAG_SPACING[key];
}

/**
 * Calculate responsive spacing based on screen size and cognitive load
 */
export function getResponsiveSpacing(
  baseSpacing: keyof typeof SPACING_SCALE,
  cognitiveLoad: number = 0.5
): {
  mobile: string;
  tablet: string;
  desktop: string;
} {
  const base = parseFloat(SPACING_SCALE[baseSpacing]);
  
  // Adjust spacing based on cognitive load
  let multiplier = 1;
  if (cognitiveLoad > 0.7) {
    multiplier = 1.3;
  } else if (cognitiveLoad < 0.3) {
    multiplier = 0.8;
  }
  
  return {
    mobile: `${base * 0.8 * multiplier}rem`,
    tablet: `${base * multiplier}rem`,
    desktop: `${base * 1.2 * multiplier}rem`
  };
}

/**
 * Generate spacing classes for Tailwind
 */
export function generateSpacingClasses(): Record<string, string> {
  const classes: Record<string, string> = {};
  
  // Standard spacing
  Object.entries(SPACING_SCALE).forEach(([key, value]) => {
    classes[`spacing-${key}`] = value;
  });
  
  // Cognitive spacing
  Object.entries(COGNITIVE_SPACING).forEach(([key, value]) => {
    classes[`spacing-cognitive-${key}`] = value;
  });
  
  // WCAG spacing
  Object.entries(WCAG_SPACING).forEach(([key, value]) => {
    classes[`spacing-wcag-${key}`] = String(value);
  });
  
  return classes;
}

/**
 * Component spacing presets for common UI patterns
 */
export const COMPONENT_SPACING = {
  button: {
    padding: {
      sm: { x: SPACING_SCALE[3], y: SPACING_SCALE[2] },
      md: { x: SPACING_SCALE[4], y: SPACING_SCALE[3] },
      lg: { x: SPACING_SCALE[6], y: SPACING_SCALE[4] }
    },
    gap: SPACING_SCALE[2]
  },
  
  card: {
    padding: {
      sm: SPACING_SCALE[4],
      md: SPACING_SCALE[6],
      lg: SPACING_SCALE[8]
    },
    gap: SPACING_SCALE[4]
  },
  
  form: {
    fieldGap: SPACING_SCALE[4],
    labelGap: SPACING_SCALE[2],
    groupGap: SPACING_SCALE[6]
  },
  
  navigation: {
    itemGap: SPACING_SCALE[1],
    sectionGap: SPACING_SCALE[6],
    padding: SPACING_SCALE[4]
  },
  
  layout: {
    sectionGap: SPACING_SCALE[12],
    containerPadding: {
      mobile: SPACING_SCALE[4],
      tablet: SPACING_SCALE[6],
      desktop: SPACING_SCALE[8]
    }
  }
} as const;

/**
 * Accessibility spacing utilities
 */
export const ACCESSIBILITY_SPACING = {
  /**
   * Ensure minimum touch target size
   */
  ensureTouchTarget: (size: string): string => {
    const sizeNum = parseFloat(size);
    const minSize = parseFloat(WCAG_SPACING.touchTarget);
    return sizeNum < minSize ? WCAG_SPACING.touchTarget : size;
  },
  
  /**
   * Calculate focus ring offset
   */
  getFocusOffset: (): string => {
    return WCAG_SPACING.focusOffset;
  },
  
  /**
   * Get text spacing for readability
   */
  getTextSpacing: (): {
    letterSpacing: string;
    wordSpacing: string;
    lineHeight: number;
  } => {
    return {
      letterSpacing: WCAG_SPACING.textSpacing,
      wordSpacing: WCAG_SPACING.textSpacing,
      lineHeight: WCAG_SPACING.lineHeightMin
    };
  }
};

/**
 * Spacing context for React components
 */
export interface SpacingContext {
  cognitiveLoad: number;
  density: keyof typeof COGNITIVE_SPACING;
  accessibility: 'standard' | 'enhanced';
}

/**
 * Get contextual spacing based on cognitive load and accessibility needs
 */
export function getContextualSpacing(
  baseSpacing: keyof typeof SPACING_SCALE,
  context: Partial<SpacingContext> = {}
): string {
  const {
    cognitiveLoad = 0.5,
    density = 'standard',
    accessibility = 'standard'
  } = context;
  
  let spacing = parseFloat(SPACING_SCALE[baseSpacing]);
  
  // Adjust for cognitive load
  if (cognitiveLoad > 0.7) {
    spacing *= 1.3; // More space for high cognitive load
  } else if (cognitiveLoad < 0.3) {
    spacing *= 0.9; // Less space for low cognitive load
  }
  
  // Adjust for density preference
  const densityMultipliers = {
    dense: 0.8,
    standard: 1,
    comfortable: 1.2,
    spacious: 1.4,
    accessible: 1.6
  };
  
  spacing *= densityMultipliers[density];
  
  // Adjust for accessibility requirements
  if (accessibility === 'enhanced') {
    spacing = Math.max(spacing, parseFloat(WCAG_SPACING.touchTarget) / 16); // Ensure minimum size
  }
  
  return `${spacing}rem`;
}

/**
 * Export spacing utilities as a single namespace
 */
export const Spacing = {
  scale: SPACING_SCALE,
  wcag: WCAG_SPACING,
  cognitive: COGNITIVE_SPACING,
  component: COMPONENT_SPACING,
  accessibility: ACCESSIBILITY_SPACING,
  get: getSpacing,
  getCognitive: getCognitiveSpacing,
  getWCAG: getWCAGSpacing,
  getResponsive: getResponsiveSpacing,
  getContextual: getContextualSpacing,
  generateClasses: generateSpacingClasses
};
